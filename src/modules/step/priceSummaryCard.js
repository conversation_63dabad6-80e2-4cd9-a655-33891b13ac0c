$(document).ready(function () {
  // 绑定价格摘要卡片的查看详情点击事件
  $('#step-price-summary-view-details').on('click', function () {
    const $element = $(this);
    const $shoppingCart = $element.closest('.shopping-cart');
    const $cartBody = $shoppingCart.find('.cart-body');
    const $arrow = $element.find('.icon-arrow-down');

    if ($cartBody.hasClass('collapsed')) {
      $cartBody.removeClass('collapsed').addClass('expanded');
      $shoppingCart.addClass('expanded');
      $arrow.css('transform', 'rotate(0deg)');
    } else {
      $cartBody.removeClass('expanded').addClass('collapsed');
      $shoppingCart.removeClass('expanded');
      $arrow.css('transform', 'rotate(-180deg)');
    }
  });
});
