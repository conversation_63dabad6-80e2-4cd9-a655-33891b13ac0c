<%
    const _always =  typeof always !== 'undefined' ? always: undefined; // 默认 pc 是不展示的，所以需要这个参数

    // 默认数据，如果没有传入数据则使用示例数据
    const cartData = locals.cartData || {
      totalAmount: 4036,
      currency: 'CNY',
      passengers: [
        { id: 1, fare: 986.1, taxes: 12.9, total: 1009 },
        { id: 2, fare: 986.1, taxes: 12.9, total: 1009 },
        { id: 3, fare: 986.1, taxes: 12.9, total: 1009 },
        { id: 4, fare: 986.1, taxes: 12.9, total: 1009 }
      ]
    };

  const fareTableJSON = JSON.stringify({
    showHeader: false,
    bordered: false,
    columns: [
      {
        title: '',
        dataIndex: 'name',
        align: 'left',
      },
      {
        title: '',
        dataIndex: 'amount',
        align: 'right',
      },
    ],
    dataSource: [
      {
        name: 'Taxes and Fees',
        amount: 'CNY 8',
      },
      {
        name: 'Fare',
        amount: 'CNY 18',
      },
      {
        name: 'Surcharge',
        amount: 'CNY 34',
      },
      {
        name: 'Service fee',
        amount: 'CNY 53',
      },
      {
        name: 'Taxes and Fees',
        amount: 'CNY 33',
      },
      {
        name: 'Taxes and Fees',
        amount: 'CNY 270',
      },
    ],
  });

%>

<div class="shopping-cart <%= _always ? 'always' : '' %>">
  <div class="cart-header">
    <div class="cart-header-content">
      <span class="total-price-label">Total price:</span>
      <div class="total-price-amount">
        <span class="currency"><%= cartData.currency %></span>
        <span class="amount"><%= cartData.totalAmount %></span>
      </div>
    </div>
    <div class="view-details" id="step-price-summary-view-details">
      <span class="view-details-text">View Details</span>
      <img class="icon-arrow-down" src="../../images/flightOverview/arrow.svg" alt="" />
    </div>
    <img class="decorative-pattern" src="../../images/flightOverview/header-logo.svg" alt="" />
  </div>

  <div class="cart-body collapsed">
    <div class="passengers-container">
      <% cartData.passengers.forEach((passenger, index) => { %>
      <div class="passenger-section">
        <div class="passenger-header">
          <span class="passenger-name">Passenger<%= passenger.id %> Adult</span>
        </div>

        <div class="fare-row">
          <div class="fare-label">
            <span>Fare:</span>
          </div>
          <span class="fare-amount"><%= cartData.currency %> <%= passenger.fare %></span>
        </div>

        <div class="taxes-row">
          <div class="taxes-label">
            <span>Taxes:</span>
            <div
              class="question-icon"
              role="tooltip"
              tabindex="0"
              data-tooltip="税费说明信息"
              data-tippy-table="<%= fareTableJSON %>">
              <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </div>
          </div>
          <span class="taxes-amount"><%= cartData.currency %> <%= passenger.taxes %></span>
        </div>

        <div class="passenger-total">
          <span class="total-label">total：</span>
          <span class="total-amount"><%= cartData.currency %> <%= passenger.total %></span>
        </div>
      </div>
      <% }); %>
    </div>
  </div>
</div>
