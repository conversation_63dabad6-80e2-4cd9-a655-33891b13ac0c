/**
 * SZ Table 组件
 * 类似 Ant Design Table 的表格组件，支持行列合并
 * 支持 Ant Design 风格的 colSpan 和 onCell 配置
 *
 * 特殊功能：
 * - 当不传入 columns 参数时，只显示 notes 内容，不显示表格
 * - 适用于只需要显示备注信息的场景
 */
class SZTable {
  // 默认配置
  static DEFAULT_OPTIONS = {
    container: '#sz-table',
    columns: [],
    dataSource: [],
    loading: false,
    rowSelection: null,
    bordered: true,
    showHeader: true,
    notes: [],
    notesTitle: '',
    sortedInfo: {},
    onChange: () => {},
  };

  constructor(options = {}) {
    // 合并默认配置
    const config = { ...SZTable.DEFAULT_OPTIONS, ...options };

    // 基础配置
    Object.assign(this, config);

    // 处理列配置
    this._processColumns();

    // DOM 元素缓存
    this._initDOMElements();

    // 内部状态
    this.mergedCells = new Map();
    this.mergedHeaders = new Map();

    // 初始化
    if (this._isValidContainer()) {
      this.init();
    }
  }

  /**
   * 处理列配置
   * @private
   */
  _processColumns() {
    this.columns = this.columns.map(column => ({
      ...column,
      key: column.key || column.dataIndex || Math.random().toString(36).substr(2, 9),
    }));
  }

  /**
   * 初始化 DOM 元素
   * @private
   */
  _initDOMElements() {
    this.$container = $(this.container);

    if (!this.$container.length) {
      console.warn(`SZTable: Container "${this.container}" not found`);
      return;
    }

    // 缓存 DOM 元素
    this.$table = this.$container.find('.sz-table');
    this.$thead = this.$container.find('.sz-table-thead');
    this.$tbody = this.$container.find('.sz-table-tbody');
    this.$empty = this.$container.find('.sz-table-empty');
    this.$loading = this.$container.find('.sz-table-loading');
    this.$notes = this.$container.find('.sz-table-notes');
    this.$notesTitle = this.$container.find('.sz-table-notes-title');
  }

  /**
   * 验证容器是否有效
   * @private
   */
  _isValidContainer() {
    if (!this.$container?.length) return false;

    if (!this.$table?.length || !this.$thead?.length || !this.$tbody?.length) {
      console.error(
        'SZTable: Required DOM elements not found. Please ensure the table template is correctly included.'
      );
      return false;
    }

    return true;
  }

  init() {
    // 验证配置
    if (!this._validateColumns() || !this._validateDataSource()) {
      console.error('SZTable: Invalid configuration, table will not be rendered');
      return;
    }

    this.render();
    this.bindEvents();
  }

  /**
   * 渲染表格
   */
  render() {
    // 如果没有列配置，只渲染备注
    if (this._isNotesOnlyMode()) {
      this._hideTable();
      this.renderNotes();
      return;
    }

    // 确保移除仅备注模式的样式类
    this.$container?.removeClass('sz-table-notes-only');

    this.updateBorderClass();
    this.renderHeader();
    this.renderBody();
    this.renderNotes();
    this.toggleLoading();
    this.toggleEmpty();
  }

  /**
   * 渲染表头
   */
  renderHeader() {
    if (!this.showHeader) {
      this.$thead?.hide();
      return;
    }

    // 确保表头显示
    this.$thead?.show();

    const headerCells = this._buildHeaderCells();
    const headerHtml = `<tr>${headerCells.join('')}</tr>`;
    this.$thead?.html(headerHtml);
  }

  /**
   * 构建表头单元格
   * @private
   */
  _buildHeaderCells() {
    const cells = [];

    // 行选择列
    if (this.rowSelection) {
      cells.push(`
        <th class="sz-table-selection-column">
          <input type="checkbox" class="sz-table-select-all" />
        </th>
      `);
    }

    // 数据列
    this.columns.forEach((column, colIndex) => {
      const cell = this._buildHeaderCell(column, colIndex);
      if (cell) cells.push(cell);
    });

    return cells;
  }

  /**
   * 构建单个表头单元格
   * @private
   */
  _buildHeaderCell(column, colIndex) {
    // 跳过 colSpan 为 0 的列
    if (column.colSpan === 0) return null;

    const mergedInfo = this.getMergedHeaderInfo(colIndex);
    if (mergedInfo.skip) return null;

    const classes = this._getHeaderCellClasses(column, colIndex);
    const attributes = this._getHeaderCellAttributes(column, colIndex);
    const content = this._getHeaderCellContent(column);

    return `<th ${attributes.join(' ')}>${content}</th>`;
  }

  /**
   * 获取表头单元格样式类
   * @private
   */
  _getHeaderCellClasses(column, colIndex) {
    const classes = [];

    if (column.sorter) classes.push('sz-table-cell-sortable');

    const align = column.align || 'center';
    classes.push(`sz-table-cell-${align}`);

    const mergedInfo = this.getMergedHeaderInfo(colIndex);
    const colspan = column.colSpan || mergedInfo.colspan;
    if (colspan > 1) classes.push('sz-table-cell-merged');

    return classes;
  }

  /**
   * 获取表头单元格属性
   * @private
   */
  _getHeaderCellAttributes(column, colIndex) {
    const attributes = [];
    const classes = this._getHeaderCellClasses(column, colIndex);

    attributes.push(`class="${classes.join(' ')}"`);
    attributes.push(`data-key="${column.key}"`);

    if (column.width) {
      attributes.push(`style="width: ${column.width}px; min-width: ${column.width}px;"`);
    }

    const mergedInfo = this.getMergedHeaderInfo(colIndex);
    const colspan = column.colSpan || mergedInfo.colspan;
    if (colspan > 1) {
      attributes.push(`colspan="${colspan}"`);
    }

    return attributes;
  }

  /**
   * 获取表头单元格内容
   * @private
   */
  _getHeaderCellContent(column) {
    let content = column.title || '';

    if (column.sorter) {
      const sortOrder = this.sortedInfo.columnKey === column.key ? this.sortedInfo.order : null;
      const iconClass = sortOrder ? `sz-table-sort-icon ${sortOrder}` : 'sz-table-sort-icon';
      content += `<span class="${iconClass}"></span>`;
    }

    return content;
  }

  /**
   * 渲染表体
   */
  renderBody() {
    if (!this.dataSource.length) {
      this.$tbody?.empty();
      return;
    }

    const rows = this.dataSource.map((record, rowIndex) => this._buildTableRow(record, rowIndex));

    this.$tbody?.html(rows.join(''));
  }

  /**
   * 构建表格行
   * @private
   */
  _buildTableRow(record, rowIndex) {
    const rowKey = record.key || rowIndex;
    const selectedClass = this.isRowSelected(rowKey) ? 'sz-table-row-selected' : '';

    const cells = this._buildRowCells(record, rowIndex);

    return `<tr data-row-key="${rowKey}" class="${selectedClass}">${cells.join('')}</tr>`;
  }

  /**
   * 构建行内所有单元格
   * @private
   */
  _buildRowCells(record, rowIndex) {
    const cells = [];

    // 行选择列
    if (this.rowSelection) {
      cells.push(this._buildSelectionCell(record, rowIndex));
    }

    // 数据列
    this.columns.forEach((column, colIndex) => {
      const cell = this._buildDataCell(record, rowIndex, column, colIndex);
      if (cell) cells.push(cell);
    });

    return cells;
  }

  /**
   * 构建选择列单元格
   * @private
   */
  _buildSelectionCell(record, rowIndex) {
    const rowKey = record.key || rowIndex;
    const checked = this.isRowSelected(rowKey) ? 'checked' : '';

    return `
      <td class="sz-table-selection-column">
        <input type="checkbox" class="sz-table-row-select" data-row-key="${rowKey}" ${checked} />
      </td>
    `;
  }

  /**
   * 构建数据单元格
   * @private
   */
  _buildDataCell(record, rowIndex, column, colIndex) {
    const cellProps = this._getCellProps(record, rowIndex, column, colIndex);

    // 跳过不需要渲染的单元格
    if (cellProps.skip) return null;

    const classes = this._getDataCellClasses(cellProps, column);
    const attributes = this._getDataCellAttributes(cellProps, rowIndex, colIndex);
    const content = this._getCellContent(record, column, rowIndex);

    return `<td ${attributes.join(' ')}>${content}</td>`;
  }

  /**
   * 获取单元格属性
   * @private
   */
  _getCellProps(record, rowIndex, column, colIndex) {
    const mergedInfo = this.getMergedCellInfo(rowIndex, colIndex);

    // 获取 onCell 返回的属性
    let onCellProps = {};
    if (column.onCell && typeof column.onCell === 'function') {
      onCellProps = column.onCell(record, rowIndex) || {};
    }

    // 跳过条件
    if (onCellProps.rowSpan === 0 || mergedInfo.skip) {
      return { skip: true };
    }

    // 合并属性
    const rowspan = onCellProps.rowSpan || mergedInfo.rowspan || 1;
    const colspan = onCellProps.colSpan || mergedInfo.colspan || 1;

    return {
      skip: false,
      rowspan,
      colspan,
      isMerged: rowspan > 1 || colspan > 1,
    };
  }

  /**
   * 获取数据单元格样式类
   * @private
   */
  _getDataCellClasses(cellProps, column) {
    const classes = [];

    const align = column.align || 'center';
    classes.push(`sz-table-cell-${align}`);

    if (cellProps.isMerged) {
      classes.push('sz-table-cell-merged');
    }

    return classes;
  }

  /**
   * 获取数据单元格属性
   * @private
   */
  _getDataCellAttributes(cellProps, rowIndex, colIndex) {
    const attributes = [];
    const classes = this._getDataCellClasses(cellProps, this.columns[colIndex]);

    // 边缘类
    const edgeClasses = this._getEdgeClasses(cellProps, rowIndex, colIndex);
    const allClasses = [...classes, ...edgeClasses];

    attributes.push(`class="${allClasses.join(' ')}"`);
    attributes.push(`data-cell-key="${rowIndex}-${colIndex}"`);

    if (cellProps.rowspan > 1) {
      attributes.push(`rowspan="${cellProps.rowspan}"`);
    }

    if (cellProps.colspan > 1) {
      attributes.push(`colspan="${cellProps.colspan}"`);
    }

    return attributes;
  }

  /**
   * 获取边缘样式类
   * @private
   */
  _getEdgeClasses(cellProps, rowIndex, colIndex) {
    if (!cellProps.isMerged) return [];

    const edgeClasses = [];

    if (colIndex === 0) {
      edgeClasses.push('sz-table-cell-left-edge');
    }

    if (colIndex + cellProps.colspan >= this.columns.length) {
      edgeClasses.push('sz-table-cell-right-edge');
    }

    if (rowIndex === 0) {
      edgeClasses.push('sz-table-cell-top-edge');
    }

    if (rowIndex + cellProps.rowspan >= this.dataSource.length) {
      edgeClasses.push('sz-table-cell-bottom-edge');
    }

    return edgeClasses;
  }

  /**
   * 获取单元格内容
   * @private
   */
  _getCellContent(record, column, rowIndex) {
    if (column.render && typeof column.render === 'function') {
      return column.render(record[column.dataIndex], record, rowIndex);
    }
    return record[column.dataIndex] || '';
  }

  /**
   * 渲染备注标题
   */
  renderNotesTitle() {
    if (!this.notesTitle) {
      this.$notesTitle?.html('');
      return;
    }

    this.$notesTitle?.html(this.notesTitle);
  }

  /**
   * 渲染备注
   */
  renderNotes() {
    // 渲染备注标题
    this.renderNotesTitle();

    // 如果没有 notes 或 notes 为空，清空显示内容
    if (!this.notes?.length) {
      this.$notes?.html('');
      return;
    }

    const notesHtml = this.notes
      .filter(note => note && typeof note === 'string')
      .map(note => `<p class="sz-table-note-item">${note}</p>`)
      .join('');

    this.$notes?.html(notesHtml);
  }

  /**
   * 获取合并单元格信息
   */
  getMergedCellInfo(rowIndex, colIndex) {
    const key = `${rowIndex}-${colIndex}`;
    return this.mergedCells.get(key) || { rowspan: 1, colspan: 1, skip: false };
  }

  /**
   * 获取合并表头信息
   */
  getMergedHeaderInfo(colIndex) {
    const key = `${colIndex}`;
    return this.mergedHeaders.get(key) || { colspan: 1, skip: false };
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.$container?.length) return;

    // 如果没有列配置，不需要绑定表格事件
    if (this._isNotesOnlyMode()) return;

    // 使用事件委托，提高性能
    this._bindSortEvents();
    this._bindSelectionEvents();
    this._bindRowClickEvents();
  }

  /**
   * 绑定排序事件
   * @private
   */
  _bindSortEvents() {
    this.$thead?.on('click', '.sz-table-cell-sortable', e => {
      e.preventDefault();
      this._handleSort($(e.currentTarget));
    });
  }

  /**
   * 绑定选择事件
   * @private
   */
  _bindSelectionEvents() {
    if (!this.rowSelection) return;

    // 全选事件
    this.$thead?.on('change', '.sz-table-select-all', e => {
      const checked = e.target.checked;
      this.$tbody?.find('.sz-table-row-select').prop('checked', checked);
      this.updateRowSelection();
    });

    // 行选择事件
    this.$tbody?.on('change', '.sz-table-row-select', () => {
      this.updateRowSelection();
    });
  }

  /**
   * 绑定行点击事件
   * @private
   */
  _bindRowClickEvents() {
    if (!this.rowSelection) return;

    this.$tbody?.on('click', 'tr', e => {
      // 避免点击复选框时重复触发
      if ($(e.target).is('input[type="checkbox"]')) return;

      const $row = $(e.currentTarget);
      const $checkbox = $row.find('.sz-table-row-select');

      if ($checkbox.length) {
        $checkbox.prop('checked', !$checkbox.prop('checked'));
        this.updateRowSelection();
      }
    });
  }

  /**
   * 处理排序
   * @private
   */
  _handleSort($th) {
    const columnKey = $th.data('key');
    const column = this.columns.find(col => col.key === columnKey);

    if (!column?.sorter) return;

    // 计算新的排序状态
    const newOrder = this._getNextSortOrder(columnKey);
    this.sortedInfo = newOrder ? { columnKey, order: newOrder } : {};

    // 执行排序
    if (newOrder && typeof column.sorter === 'function') {
      this._sortDataSource(column.sorter, newOrder);
      this.renderBody();
    }

    this.renderHeader();
    this.onChange?.(null, null, this.sortedInfo);
  }

  /**
   * 获取下一个排序状态
   * @private
   */
  _getNextSortOrder(columnKey) {
    if (this.sortedInfo.columnKey !== columnKey) {
      return 'ascend';
    }

    switch (this.sortedInfo.order) {
      case 'ascend':
        return 'descend';
      case 'descend':
        return null;
      default:
        return 'ascend';
    }
  }

  /**
   * 排序数据源
   * @private
   */
  _sortDataSource(sorter, order) {
    this.dataSource.sort((a, b) => {
      const result = sorter(a, b);
      return order === 'descend' ? -result : result;
    });
  }

  /**
   * 更新行选择状态
   */
  updateRowSelection() {
    if (!this.rowSelection) return;

    const { selectedRowKeys, selectedRows } = this._getSelectedData();

    this._updateSelectAllState(selectedRowKeys.length);
    this._updateRowStyles(selectedRowKeys);
    this._updateRowSelectionState(selectedRowKeys);

    // 触发回调
    this.rowSelection.onChange?.(selectedRowKeys, selectedRows);
  }

  /**
   * 获取选中的数据
   * @private
   */
  _getSelectedData() {
    const selectedRowKeys = [];
    const selectedRows = [];

    this.$tbody?.find('.sz-table-row-select:checked').each((_, checkbox) => {
      const rowKey = $(checkbox).data('row-key');
      const record = this.dataSource.find(item => (item.key || this.dataSource.indexOf(item)) == rowKey);

      selectedRowKeys.push(rowKey);
      if (record) selectedRows.push(record);
    });

    return { selectedRowKeys, selectedRows };
  }

  /**
   * 更新全选状态
   * @private
   */
  _updateSelectAllState(selectedCount) {
    const $selectAll = this.$thead?.find('.sz-table-select-all');
    if (!$selectAll?.length) return;

    const totalRows = this.dataSource.length;

    if (selectedCount === 0) {
      $selectAll.prop({ checked: false, indeterminate: false });
    } else if (selectedCount === totalRows) {
      $selectAll.prop({ checked: true, indeterminate: false });
    } else {
      $selectAll.prop({ checked: false, indeterminate: true });
    }
  }

  /**
   * 更新行样式
   * @private
   */
  _updateRowStyles(selectedRowKeys) {
    this.$tbody?.find('tr').removeClass('sz-table-row-selected');

    selectedRowKeys.forEach(key => {
      this.$tbody?.find(`tr[data-row-key="${key}"]`).addClass('sz-table-row-selected');
    });
  }

  /**
   * 更新行选择状态
   * @private
   */
  _updateRowSelectionState(selectedRowKeys) {
    this.rowSelection.selectedRowKeys = selectedRowKeys;
  }

  /**
   * 检查行是否被选中
   */
  isRowSelected(rowKey) {
    return this.rowSelection?.selectedRowKeys?.includes(rowKey) || false;
  }

  /**
   * 验证列配置
   * @private
   */
  _validateColumns() {
    // 如果没有列配置，允许通过验证（只显示备注）
    if (!Array.isArray(this.columns) || this.columns.length === 0) {
      return true;
    }

    return this.columns.every((column, index) => {
      if (!column.dataIndex && !column.render) {
        console.warn(`SZTable: Column at index ${index} must have either dataIndex or render function`);
        return false;
      }
      return true;
    });
  }

  /**
   * 验证数据源
   * @private
   */
  _validateDataSource() {
    if (!Array.isArray(this.dataSource)) {
      console.warn('SZTable: dataSource must be an array');
      return false;
    }
    return true;
  }

  /**
   * 安全执行函数
   * @private
   */
  _safeExecute(fn, ...args) {
    try {
      return fn?.(...args);
    } catch (error) {
      console.error('SZTable: Error executing function:', error);
      return null;
    }
  }

  /**
   * 切换加载状态
   */
  toggleLoading() {
    if (this.loading) {
      this.$loading?.show();
      this.$table?.hide();
    } else {
      this.$loading?.hide();
      this.toggleEmpty();
    }
  }

  /**
   * 切换空状态
   */
  toggleEmpty() {
    const isEmpty = !this.loading && !this.dataSource.length;

    if (isEmpty) {
      this.$empty?.show();
      this.$table?.hide();
    } else {
      this.$empty?.hide();
      this.$table?.show();
    }
  }

  /**
   * 隐藏表格，只显示备注
   * @private
   */
  _hideTable() {
    this.$table?.hide();
    this.$empty?.hide();
    this.$loading?.hide();

    // 在仅备注模式下，隐藏容器边框
    this.$container?.addClass('sz-table-notes-only');
  }

  /**
   * 检查是否只显示备注模式
   * @private
   */
  _isNotesOnlyMode() {
    return !this.columns || !this.columns.length;
  }

  /**
   * 更新边框样式类
   */
  updateBorderClass() {
    if (!this.$container?.length) return;

    if (this.bordered) {
      this.$container.removeClass('sz-table-no-border');
    } else {
      this.$container.addClass('sz-table-no-border');
    }
  }

  /**
   * 更新数据源
   */
  updateDataSource(dataSource) {
    if (!Array.isArray(dataSource)) {
      console.warn('SZTable: dataSource must be an array');
      return;
    }

    this.updateSingleConfig('dataSource', dataSource);
  }

  /**
   * 清理无效的合并单元格配置
   * @private
   */
  _cleanupInvalidMergedCells() {
    if (this.mergedCells.size === 0) return;

    const validMergedCells = new Map();

    this.mergedCells.forEach((value, key) => {
      const [rowIndex, colIndex] = key.split('-').map(Number);
      if (rowIndex < this.dataSource.length && colIndex < this.columns.length) {
        validMergedCells.set(key, value);
      }
    });

    this.mergedCells = validMergedCells;
  }

  /**
   * 更新备注
   */
  updateNotes(notes) {
    const validNotes = Array.isArray(notes) ? notes : [];
    this.updateSingleConfig('notes', validNotes);
  }

  /**
   * 更新备注标题
   */
  updateNotesTitle(notesTitle) {
    this.updateSingleConfig('notesTitle', notesTitle || '');
  }

  /**
   * 设置加载状态
   */
  setLoading(loading) {
    this.updateSingleConfig('loading', loading);
  }

  /**
   * 更新列配置
   */
  updateColumns(columns) {
    if (!Array.isArray(columns)) {
      console.warn('SZTable: columns must be an array');
      return;
    }

    this.updateSingleConfig('columns', columns);
  }

  /**
   * 更新表头显示状态
   */
  updateShowHeader(showHeader) {
    this.updateSingleConfig('showHeader', showHeader);
  }

  /**
   * 更新边框显示状态
   */
  updateBordered(bordered) {
    this.updateSingleConfig('bordered', bordered);
  }

  /**
   * 获取选中的行
   */
  getSelectedRows() {
    if (!this.rowSelection) {
      return { selectedRowKeys: [], selectedRows: [] };
    }

    return this._getSelectedData();
  }

  /**
   * 设置选中的行
   */
  setSelectedRows(selectedRowKeys = []) {
    if (!this.rowSelection || !Array.isArray(selectedRowKeys)) return;

    this.rowSelection.selectedRowKeys = selectedRowKeys;

    this.$tbody?.find('.sz-table-row-select').each((_, checkbox) => {
      const rowKey = $(checkbox).data('row-key');
      $(checkbox).prop('checked', selectedRowKeys.includes(rowKey));
    });

    this.updateRowSelection();
  }

  /**
   * 销毁组件
   */
  destroy() {
    // 解绑事件
    this.$container?.off();

    // 清理内部状态
    this.mergedCells?.clear();
    this.mergedHeaders?.clear();

    // 清空 DOM 引用
    this.$container = null;
    this.$table = null;
    this.$thead = null;
    this.$tbody = null;
    this.$empty = null;
    this.$loading = null;
    this.$notes = null;
    this.$notesTitle = null;
  }

  /**
   * 刷新表格
   */
  refresh() {
    this.render();
  }

  /**
   * 更新表格配置并重新渲染
   * @param {Object} newConfig - 新的配置对象
   */
  updateConfig(newConfig = {}) {
    // 合并新配置
    Object.assign(this, newConfig);

    // 重新处理列配置
    if (newConfig.columns) {
      this._processColumns();
    }

    // 清理无效的合并单元格配置
    this._cleanupInvalidMergedCells();

    // 重新渲染
    this.render();
  }

  /**
   * 批量更新配置的便捷方法
   * @param {Object} updates - 包含要更新的配置项
   * @param {boolean} forTippy - 是否为 tippy 模式，返回处理好的内容
   */
  batchUpdate(updates = {}, forTippy = false) {
    const { columns, dataSource, showHeader, notes, notesTitle, loading, bordered, ...otherConfig } = updates;

    // 更新基础配置
    Object.assign(this, otherConfig);

    // 更新列配置
    if (columns) {
      this.columns = columns;
      this._processColumns();
    }

    // 更新数据源
    if (dataSource) {
      this.dataSource = dataSource;
    }

    // 更新显示表头
    if (showHeader !== undefined) {
      this.showHeader = showHeader;
    }

    // 更新备注
    if (notes !== undefined) {
      this.notes = notes;
    }

    // 更新备注标题
    if (notesTitle !== undefined) {
      this.notesTitle = notesTitle;
    }

    // 更新加载状态
    if (loading !== undefined) {
      this.loading = loading;
    }

    // 更新边框状态
    if (bordered !== undefined) {
      this.bordered = bordered;
    }

    // 清理无效的合并单元格配置
    this._cleanupInvalidMergedCells();

    // 统一重新渲染
    this.render();

    // 如果是 tippy 模式，返回处理好的内容
    if (forTippy) {
      let tableContent = $(this.container).html();

      // 构建样式类数组
      const cssClasses = ['sz-table'];

      // 仅备注模式
      if (this._isNotesOnlyMode()) {
        cssClasses.push('sz-table-notes-only');
      }
      // 无边框模式
      else if (this.bordered === false) {
        cssClasses.push('sz-table-no-border');
      }

      tableContent = `<div class="${cssClasses.join(' ')}">${tableContent}</div>`;

      return tableContent;
    }
  }

  /**
   * 动态更新单个配置项
   * @param {string} key - 配置项名称
   * @param {*} value - 配置项值
   */
  updateSingleConfig(key, value) {
    if (key === 'columns') {
      this.columns = value;
      this._processColumns();
    } else if (key === 'dataSource') {
      this.dataSource = value;
      this._cleanupInvalidMergedCells();
    } else {
      this[key] = value;
    }

    // 重新渲染
    this.render();
  }

  /**
   * 重置表格到初始状态
   */
  reset() {
    // 重置到默认配置
    const defaultConfig = { ...SZTable.DEFAULT_OPTIONS };
    Object.assign(this, defaultConfig);

    // 清理状态
    this.mergedCells.clear();
    this.mergedHeaders.clear();

    // 重新处理列配置
    this._processColumns();

    // 重新渲染
    this.render();
  }

  /**
   * 获取表格配置
   */
  getConfig() {
    return {
      columns: this.columns,
      dataSource: this.dataSource,
      loading: this.loading,
      rowSelection: this.rowSelection,
      bordered: this.bordered,
      showHeader: this.showHeader,
      notes: this.notes,
      notesTitle: this.notesTitle,
      sortedInfo: this.sortedInfo,
    };
  }

  /**
   * 初始化 tippy table 功能
   * 静态方法，用于全局初始化所有带有 data-tippy-table 属性的元素
   */
  static initTippyTable() {
    const $tableTippy = $('[data-tippy-table]');

    $tableTippy.each((index, element) => {
      const $tableItem = $(element);
      const tableProps = $tableItem.data('tippy-table');

      const _table = new SZTable();

      if (tableProps) {
        tippy(element, {
          theme: 'table-tippy',
          content: $(_table.container).html(),
          appendTo: () => document.body,
          allowHTML: true,
          interactive: true,
          // trigger: 'click',
          onShow(instance) {
            const _onCellMap = {
              // 退票
              refund: (_, index) => {
                if (index === 0) {
                  return { rowSpan: 2 };
                }
                if (index === 1) {
                  return { rowSpan: 0 };
                }
                if (index === 2) {
                  return { rowSpan: 2 };
                }
                if (index === 3) {
                  return { rowSpan: 0 };
                }
                return {};
              },
              // 改签
              change: (_, index) => {
                if (index === 0) {
                  return { rowSpan: 2 };
                }
                if (index === 1) {
                  return { rowSpan: 0 };
                }
                if (index === 2) {
                  return { rowSpan: 2 };
                }
                if (index === 3) {
                  return { rowSpan: 0 };
                }
                return {};
              },
            };

            const _columns = tableProps.columns || [];
            _columns.forEach(item => {
              if (item._onCellType) {
                item.onCell = _onCellMap[item._onCellType];
              }
            });

            // 使用批量更新方法，统一处理配置更新、重渲染和内容生成
            const tableContent = _table.batchUpdate(
              {
                columns: _columns,
                dataSource: tableProps.dataSource || [],
                notesTitle: tableProps.notesTitle,
                notes: tableProps.notes || [],
                showHeader: tableProps.showHeader === undefined ? true : tableProps.showHeader, // 默认显示表头
                bordered: tableProps.bordered === undefined ? true : tableProps.bordered, // 默认显示边框
              },
              true
            ); // 传入 true 表示 tippy 模式

            // 更新 tippy 的内容
            instance.setContent(tableContent);
          },
        });
      }
    });
  }
}

// 自动初始化 tippy table 功能
$(document).ready(function () {
  SZTable.initTippyTable();
});
