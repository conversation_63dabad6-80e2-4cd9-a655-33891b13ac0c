@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

.sz-table,
.tippy-box[data-theme~='table-tippy'] {
  .sz-table-wrapper {
    position: relative;
    background: @gray-0;
    border: 1px solid @gray-1; // 外层统一边框
    border-radius: 8px; // 外层圆角
    overflow: hidden; // 确保圆角效果，内容不会超出

    .sz-table-container {
      overflow-x: auto;
      overflow-y: hidden;

      .sz-table {
        width: 100%;
        border-collapse: collapse; // 使用 collapse 让内部分隔线正确合并
        border-spacing: 0;
        font-size: 16px;
        color: @gray-5;
        background: @gray-0;
        // 不设置表格边框，使用外层容器的边框

        .screenMobile({
          font-size: 12px;
        });

        .sz-table-thead {
          background: @gray-1_1;

          tr {
            th {
              padding: 10px 6px;
              text-align: center;
              font-weight: 400;
              color: @gray-5;
              // white-space: nowrap;
              position: relative;
              vertical-align: middle;

              // 只设置内部分隔线，不设置外边框
              border-right: 1px solid @gray-1;
              border-bottom: 1px solid @gray-1;

              .screenMobile({
                padding: 8px 6px;
              });

              // 移除最后一列的右边框（外层容器提供）
              &:last-child {
                border-right: none;
              }

              // 表头下方的品牌色装饰线
              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 3px;
                background: @brand-2;
                border-radius: 0 0 8px 8px;

                .screenMobile({
                  width: 30px;
                });
              }

              &.sz-table-cell-sortable {
                cursor: pointer;
                user-select: none;

                &:hover {
                  background: fade(@gray-1_1, 80%);
                }

                .sz-table-sort-icon {
                  display: inline-block;
                  margin-left: 4px;
                  width: 12px;
                  height: 12px;
                  vertical-align: middle;
                  opacity: 0.6;

                  &.active {
                    opacity: 1;
                    color: @brand-2;
                  }
                }
              }

              &.sz-table-cell-left {
                text-align: left;
              }

              &.sz-table-cell-right {
                text-align: right;
              }
            }
          }
        }

        .sz-table-tbody {
          tr {
            transition: background-color 0.2s;

            &:hover {
              background: fade(@gray-1_1, 30%);
            }

            &.sz-table-row-selected {
              background: fade(@brand-2, 5%);
            }

            td {
              padding: 10px 6px;
              vertical-align: middle;
              word-break: break-word;
              text-align: center;

              // 只设置内部分隔线，不设置外边框
              border-right: 1px solid @gray-1;
              border-bottom: 1px solid @gray-1;

              .screenMobile({
                padding: 8px 6px;
              });

              // 移除最后一列的右边框（外层容器提供）
              &:last-child {
                border-right: none;
              }

              &.sz-table-cell-left {
                text-align: left;
              }

              &.sz-table-cell-right {
                text-align: right;
              }

              &.sz-table-cell-merged {
                background: @gray-0;
              }

              // 合并单元格边缘处理 - 移除与外层边框重叠的边框
              // 左边缘、右边缘、上边缘合并单元格不需要特殊处理
              // 右边缘合并单元格已经通过 :last-child 移除了右边框

              &.sz-table-cell-bottom-edge {
                // 下边缘合并单元格需要移除下边框，避免与外层容器边框重叠
                border-bottom: none !important;
              }
            }

            // 移除最后一行的下边框（外层容器提供）
            &:last-child td {
              border-bottom: none;
            }
          }
        }
      }
    }

    // 空状态样式
    .sz-table-empty {
      padding: 60px 20px;
      text-align: center;

      &-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      &-image {
        width: 64px;
        height: 64px;
        margin-bottom: 16px;
        opacity: 0.4;
      }

      &-text {
        color: @gray-light;
        font-size: 14px;
      }
    }

    // 加载状态样式
    .sz-table-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      &-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      &-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid @gray-2;
        border-top: 2px solid @brand-1;
        border-radius: 50%;
        animation: sz-table-spin 1s linear infinite;
        margin-bottom: 12px;
      }

      &-text {
        color: @gray-3;
        font-size: 14px;
      }
    }
  }

  .sz-table-notes-title {
    margin: 20px 0;

    .screenMobile({
      margin: 10px 0;
    });

    font-size: 16px;
    font-weight: 500;
    color: @brand-1;
  }

  // 备注样式
  .sz-table-notes {
    .sz-table-note-item {
      margin: 0 0 8px 0;

      &:nth-child(1) {
        margin-top: 20px;
      }

      font-size: 16px;
      color: @gray-5;
      line-height: 1.5;

      .screenMobile({
        font-size: 12px;
        line-height: 1.2;
      });

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 无边框样式
  &.sz-table-no-border {
    .sz-table-wrapper {
      border: none !important;
      border-radius: 0 !important;

      .sz-table {
        .sz-table-thead {
          tr {
            th {
              border-right: none !important;
              border-bottom: none !important;
            }
          }
        }

        .sz-table-tbody {
          tr {
            td {
              border-right: none !important;
              border-bottom: none !important;
            }

            // 确保最后一行的边框也被移除
            &:last-child td {
              border-bottom: none !important;
            }
          }
        }
      }
    }
  }

  // 仅备注模式样式
  &.sz-table-notes-only {
    .sz-table-wrapper {
      border: none !important;
      border-radius: 0 !important;
      background: transparent !important;
      padding: 0 !important;
    }

    .sz-table-notes-title {
      margin: 0 0 10px;

      font-size: 16px;
      font-weight: 500;
      color: @brand-1;
    }

    // 确保备注样式在仅备注模式下正常显示
    .sz-table-notes {
      .sz-table-note-item {
        &:nth-child(1) {
          margin-top: 0; // 移除第一个备注的上边距
        }
      }
    }
  }
}

// Tippy 弹窗
.tippy-box[data-theme~='table-tippy'] {
  width: 690px;
  max-width: 100%;

  .screenMobile({
    width: 305px;
    max-width: 100%;
  });
}

// 旋转动画
@keyframes sz-table-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 排序图标样式
.sz-table-sort-icon {
  &::before {
    content: '↕';
  }

  &.asc::before {
    content: '↑';
  }

  &.desc::before {
    content: '↓';
  }
}
