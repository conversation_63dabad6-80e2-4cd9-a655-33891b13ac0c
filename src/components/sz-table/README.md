# SZ Table 组件

一个类似 Ant Design Table 的表格组件，支持行列合并、排序、选择等功能。UI 设计基于 MasterGo 设计稿，具有现代化的视觉效果。

## 设计特点

- **设计稿匹配**：完全按照 MasterGo 设计稿实现的 UI 样式
- **虚线边框**：使用虚线边框 (#DBD7D4) 营造轻盈感
- **品牌色装饰**：表头带有品牌色 (#942531) 下划线装饰
- **圆角设计**：表格四角 8px 圆角，现代化视觉
- **居中对齐**：默认文本居中对齐，符合设计规范
- **16px 字体**：使用 Source Han Sans 16px 字体，清晰易读
- **智能边框**：使用 border-collapse 避免合并单元格时的边框重叠问题

## 基本用法

```html
<!-- 在 HTML 中引入组件模板 -->
<%- include('../components/sz-table/index.ejs', { id: 'my-table', className: 'custom-table' }) %>
```

```javascript
// 初始化表格
const table = new SZTable({
  container: '#my-table',
  columns: [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      sorter: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      align: 'center',
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (text, record) => `<span title="${text}">${text}</span>`,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (text, record, index) => `
        <button class="btn-edit" data-id="${record.id}">编辑</button>
        <button class="btn-delete" data-id="${record.id}">删除</button>
      `,
    },
  ],
  dataSource: [
    {
      key: '1',
      name: '张三',
      age: 32,
      address: '北京市朝阳区',
    },
    {
      key: '2',
      name: '李四',
      age: 28,
      address: '上海市浦东新区',
    },
  ],
});
```

## API

### 构造函数参数

| 参数         | 说明                       | 类型     | 默认值      |
| ------------ | -------------------------- | -------- | ----------- |
| container    | 表格容器选择器             | string   | '#sz-table' |
| columns      | 表格列的配置               | Array    | []          |
| dataSource   | 数据数组                   | Array    | []          |
| loading      | 页面是否加载中             | boolean  | false       |
| rowSelection | 表格行是否可选择           | object   | null        |
| bordered     | 是否展示外边框和列边框     | boolean  | true        |
| showHeader   | 是否显示表头               | boolean  | true        |
| notes        | 表格下方的备注数组         | Array    | []          |
| sortedInfo   | 当前排序状态               | object   | {}          |
| onChange     | 排序变化时触发             | function | -           |

### columns 配置

| 参数      | 说明                                                     | 类型                          | 默认值   |
| --------- | -------------------------------------------------------- | ----------------------------- | -------- |
| title     | 列头显示文字                                             | string                        | -        |
| dataIndex | 列数据在数据项中对应的路径                               | string                        | -        |
| key       | 列的唯一标识，如果已经设置了唯一的 dataIndex，可以忽略   | string                        | 自动生成 |
| width     | 列宽度（像素）                                           | number                        | -        |
| align     | 设置列的对齐方式                                         | 'left' \| 'right' \| 'center' | 'center' |
| sorter    | 排序函数，本地排序使用一个函数，需要服务端排序可设为 true | function \| boolean           | -        |
| render    | 生成复杂数据的渲染函数                                   | function(text, record, index) | -        |
| onCell    | 设置单元格属性，用于行列合并                             | function(record, rowIndex)    | -        |
| colSpan   | 表头列合并，设置为 0 时不渲染                            | number                        | -        |

### rowSelection 配置

| 参数            | 说明                            | 类型                                                  | 默认值 |
| --------------- | ------------------------------- | ----------------------------------------------------- | ------ |
| selectedRowKeys | 指定选中项的 key 数组           | Array                                                 | []     |
| onChange        | 选中项发生变化时的回调          | function(selectedRowKeys, selectedRows)               | -      |
| onSelect        | 用户手动选择/取消选择某行的回调 | function(record, selected, selectedRows, nativeEvent) | -      |

## 方法

| 方法名             | 说明                   | 参数                                      |
| ------------------ | ---------------------- | ----------------------------------------- |
| updateDataSource   | 更新数据源             | (dataSource: Array)                       |
| updateColumns      | 更新列配置             | (columns: Array)                          |
| setLoading         | 设置加载状态           | (loading: boolean)                        |
| updateShowHeader   | 更新表头显示状态       | (showHeader: boolean)                     |
| updateBordered     | 更新边框显示状态       | (bordered: boolean)                       |
| updateNotes        | 更新表格备注           | (notes: Array)                            |
| getSelectedRows    | 获取选中的行           | -                                         |
| setSelectedRows    | 设置选中的行           | (selectedRowKeys: Array)                  |
| updateConfig       | 批量更新配置并重新渲染 | (newConfig: Object)                       |
| batchUpdate        | 批量更新配置的便捷方法 | (updates: Object, forTippy?: boolean)     |
| updateSingleConfig | 动态更新单个配置项     | (key: string, value: any, immediate?: boolean) |
| refresh            | 刷新表格               | -                                         |
| reset              | 重置表格到初始状态     | -                                         |
| getConfig          | 获取当前表格配置       | -                                         |
| destroy            | 销毁组件               | -                                         |

## 行列合并功能

SZTable 支持类似 Ant Design 的行列合并功能，通过 `onCell` 函数和 `colSpan` 属性实现。

### 使用 onCell 进行数据行合并

```javascript
const table = new SZTable({
  container: '#my-table',
  columns: [
    {
      title: '产品',
      dataIndex: 'product',
      key: 'product',
      onCell: (record, rowIndex) => {
        // 合并相同产品的单元格
        if (rowIndex === 0) {
          return { rowSpan: 2 }; // 向下合并2行
        }
        if (rowIndex === 1) {
          return { rowSpan: 0 }; // 不渲染此单元格
        }
        return {};
      },
    },
    {
      title: '规格',
      dataIndex: 'spec',
      key: 'spec',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      onCell: (record, rowIndex) => {
        // 横向合并单元格
        if (rowIndex === 2) {
          return { colSpan: 2 }; // 向右合并2列
        }
        return {};
      },
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      onCell: (record, rowIndex) => {
        if (rowIndex === 2) {
          return { colSpan: 0 }; // 不渲染此单元格（被左侧合并）
        }
        return {};
      },
    },
  ],
  dataSource: [
    { key: '1', product: '苹果', spec: '大', quantity: 10, price: 5.0 },
    { key: '2', product: '苹果', spec: '小', quantity: 20, price: 3.0 },
    { key: '3', product: '橙子', spec: '中', quantity: 15, price: 4.0 },
  ],
});
```

### 使用 colSpan 进行表头合并

```javascript
const table = new SZTable({
  container: '#my-table',
  columns: [
    {
      title: '基本信息',
      dataIndex: 'info',
      key: 'info',
      colSpan: 2, // 合并2列表头
    },
    {
      title: '', // 被合并的列，title 可以为空
      dataIndex: 'detail',
      key: 'detail',
      colSpan: 0, // 不渲染此表头
    },
    {
      title: '价格信息',
      dataIndex: 'price',
      key: 'price',
    },
  ],
  // ... 其他配置
});
```

### onCell 返回值说明

`onCell` 函数可以返回以下属性：

- `rowSpan`: 向下合并的行数，设置为 0 表示不渲染此单元格
- `colSpan`: 向右合并的列数，设置为 0 表示不渲染此单元格

```javascript
// onCell 函数示例
onCell: (record, rowIndex) => {
  if (rowIndex === 0) {
    return { rowSpan: 2, colSpan: 1 }; // 向下合并2行，向右合并1列
  }
  if (rowIndex === 1) {
    return { rowSpan: 0 }; // 不渲染此单元格
  }
  return {}; // 正常渲染
}
```

## 表格备注功能

表格支持在下方显示多个段落备注，用于补充说明表格内容。

### 基本用法

```javascript
const table = new SZTable({
  container: '#my-table',
  columns: [...],
  dataSource: [...],
  notes: [
    '备注1：此表格数据仅供参考，实际情况可能有所不同。',
    '备注2：价格信息更新时间为2024年1月1日。',
    '备注3：如有疑问请联系客服。'
  ]
});
```

### 动态更新备注

```javascript
// 更新备注内容
table.updateNotes([
  '新的备注信息1',
  '新的备注信息2'
]);

// 清空备注
table.updateNotes([]);
```

## Tippy 弹窗表格功能

SZTable 支持作为 Tippy.js 弹窗内容使用，适用于悬浮显示详细信息的场景。

### 基本用法

```html
<!-- HTML 中添加触发元素 -->
<span data-tippy-table='{"columns": [...], "dataSource": [...]}'>
  悬浮查看详情
</span>
```

```javascript
// 自动初始化所有 tippy table
SZTable.initTippyTable();
```

### 配置示例

```html
<span data-tippy-table='{
  "columns": [
    {"title": "项目", "dataIndex": "item"},
    {"title": "费用", "dataIndex": "fee", "_onCellType": "refund"}
  ],
  "dataSource": [
    {"item": "退票费", "fee": "¥100"},
    {"item": "手续费", "fee": "¥50"}
  ],
  "notes": ["备注：费用仅供参考"],
  "showHeader": true,
  "bordered": true
}'>
  查看费用明细
</span>
```

### 预定义合并类型

通过 `_onCellType` 可以使用预定义的单元格合并模式：

- `refund`: 退票费用合并模式
- `change`: 改签费用合并模式

## 完整示例

```javascript
const table = new SZTable({
  container: '#example-table',
  columns: [
    {
      title: '产品名称',
      dataIndex: 'product',
      key: 'product',
      width: 150,
      onCell: (record, rowIndex) => {
        // 合并相同产品的行
        if (record.product === '苹果') {
          if (rowIndex === 0) return { rowSpan: 2 };
          if (rowIndex === 1) return { rowSpan: 0 };
        }
        return {};
      },
    },
    {
      title: '规格',
      dataIndex: 'spec',
      key: 'spec',
      width: 100,
      align: 'center',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'right',
      sorter: (a, b) => a.quantity - b.quantity,
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      align: 'right',
      render: text => `¥${text.toFixed(2)}`,
    },
  ],
  dataSource: [
    { key: '1', product: '苹果', spec: '大', quantity: 10, price: 5.0 },
    { key: '2', product: '苹果', spec: '小', quantity: 20, price: 3.0 },
    { key: '3', product: '橙子', spec: '中', quantity: 15, price: 4.0 },
  ],
  notes: [
    '备注：以上价格为市场参考价，实际价格可能因地区而异。',
    '说明：数量单位为公斤，价格单位为元/公斤。'
  ],
  rowSelection: {
    selectedRowKeys: [],
    onChange: (selectedRowKeys, selectedRows) => {
      console.log('选中的行:', selectedRowKeys, selectedRows);
    },
  },
  onChange: (pagination, filters, sorter) => {
    console.log('排序变化:', sorter);
  },
});
```

## 性能优化特性

- **防抖渲染**：使用 `_debouncedRender()` 避免频繁更新时的性能问题
- **事件委托**：使用事件委托减少事件监听器数量
- **循环优化**：使用传统 for 循环替代 forEach 提升性能
- **Set 查找**：使用 Set 进行 O(1) 时间复杂度的查找操作
- **内存管理**：proper cleanup 防止内存泄漏

## 动态更新示例

```javascript
// 动态更新数据源
table.updateDataSource([
  { key: '1', name: '新数据1', age: 25 },
  { key: '2', name: '新数据2', age: 30 },
]);

// 批量更新多个配置
table.batchUpdate({
  dataSource: newData,
  loading: false,
  notes: ['更新后的备注信息'],
  showHeader: true,
});

// 单个配置更新（使用防抖）
table.updateSingleConfig('loading', true);

// 立即更新（不使用防抖）
table.updateSingleConfig('bordered', false, true);
```

## 错误处理

组件内置了完善的错误处理机制：

- **渲染函数错误**：`column.render` 函数执行错误时会显示空内容并记录错误日志
- **onCell 函数错误**：`column.onCell` 函数执行错误时会使用默认单元格属性
- **配置验证**：自动验证 columns 和 dataSource 配置的有效性
- **边界值处理**：rowspan 和 colspan 自动确保最小值为 1

## 边框处理

组件采用了优化的边框策略来处理合并单元格时的边框重叠问题：

- 使用 `border-collapse: collapse` 避免边框重叠
- 所有单元格统一使用 `border: 1px dashed #DBD7D4`
- 合并单元格时边框会自动正确处理，不会出现加粗现象
- 支持通过 `bordered` 属性控制边框显示

## 样式定制

组件使用 Less 编写样式，可以通过覆盖 CSS 变量来定制样式：

```less
.sz-table-wrapper {
  // 自定义表格样式
  .sz-table {
    // 自定义表格内部样式
    border-radius: 8px; // 圆角
    
    th {
      background-color: #f5f5f5;
      border-bottom: 2px solid #942531; // 品牌色装饰
    }

    th,
    td {
      border: 1px dashed #DBD7D4; // 虚线边框
      padding: 12px 16px;
      text-align: center;
      font-size: 16px;
      font-family: 'Source Han Sans', sans-serif;
    }
  }
  
  // 无边框模式
  &.sz-table-no-border .sz-table {
    th, td {
      border: none;
    }
  }
}
```

## 注意事项

1. **容器要求**：确保 HTML 模板正确引入，包含必要的 DOM 结构
2. **jQuery 依赖**：组件依赖 jQuery，确保在使用前已加载
3. **Tippy.js 依赖**：使用 Tippy 功能时需要加载 Tippy.js 库
4. **内存管理**：组件销毁时会自动清理事件和定时器，避免内存泄漏
5. **性能考虑**：大数据量时建议使用防抖更新，避免频繁重渲染
