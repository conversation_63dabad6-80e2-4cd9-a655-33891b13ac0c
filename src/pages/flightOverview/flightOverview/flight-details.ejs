<% 
  const _isStop = typeof isStop !== 'undefined' ? isStop : undefined;

  const mealTableJSON = JSON.stringify({
      columns: [
        {
          title: 'Flight number',
          dataIndex: 'flightNumber',
        },
        {
          title: 'Leg',
          dataIndex: 'leg',
        },
        {
          title: 'Meals',
          dataIndex: 'meals',
        },
      ],
      dataSource: [
        {
          key: '1',
          flightNumber: 'ZH307',
          leg: 'Shenzhen-Shanghai',
          meals: 'Dinner',
        },
        {
          key: '2',
          flightNumber: 'ZH8034',
          leg: 'Shanghai-Beijin',
          meals: 'Dim sum',
        },
      ],
      notesTitle: 'Tips:',
      notes: [
        '1. The specific types of meals are subject to the actual equipment of the flight.',
        '2. Meals for codeshare flights shall be based on the actual operating flight;',
        '3. In case of special circumstances such as flight delays, changes in aircraft types, or sudden public health emergencies that may result in adjustments to the meal plan, please refer to the actual configuration of the flight;'
      ],
    });
%>

<div class="flight-details">
  <div class="time-details">
    <div class="departure">
      <div class="time">10:30</div>
      <div class="date">2025-07-01</div>
    </div>

    <div class="duration">
      <div class="divider"></div>
      <div class="duration-info">
        <span class="duration-value">3h 0min</span>
        <% if(_isStop){ %>
        <span>Stopping in Shanghai</span>
        <span>Stopping in guangzhou</span>
        <% } %>
      </div>
      <div class="divider"></div>
    </div>

    <div class="arrival">
      <div class="date">2025-07-01</div>
      <div class="time">13:30</div>
    </div>
  </div>

  <div class="airport-details">
    <div class="departure-airport">shenzhen international airport T1</div>
    <div class="arrival-airport">BeiJing Capital Airport T1</div>
  </div>

  <div class="flight-meal-info">
    <div class="flight-info">
      <div class="flight-number">
        <img src="../../images/flightOptions/airline-logo.svg" />
        ZH8034
      </div>
      <div class="aircraft-type">Boeing 738</div>
      <div class="shared-flight">共享</div>
    </div>

    <div class="meal-info">
      <div
        class="flight-details-link"
        role="button"
        tabindex="0"
        aria-label="Meal information"
        data-tippy-table="<%= mealTableJSON %>">
        <img class="icon" src="../../images/flightOptions/meals.svg" alt="Meal icon" />
        <span>Meal</span>
      </div>

      <div>Business class/J class</div>
    </div>
  </div>

  <div class="carrier-info">Actual Carrier: Shenzhen Airlines</div>
</div>
