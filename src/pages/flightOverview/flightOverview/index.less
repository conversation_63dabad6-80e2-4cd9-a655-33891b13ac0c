@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.flight-overview {
  display: flex;

  &-left {
    &-content {
      border: 1px solid @brand-1;
      border-radius: 8px;
      margin-bottom: 20px;

      .segment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #e64343; // brand-4
        color: #ffffff; // gray-0
        padding: 10px 20px;
        border-radius: 8px 8px 0 0;
        font-size: 20px;

        .screenMobile({
          font-size: 16px;
        });
      }

      .flight-details {
        padding: 20px;

        .screenMobile({
          padding: 10px;
        });

        .time-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;

          .departure,
          .arrival {
            display: flex;
            align-items: baseline;

            .screenMobile({
              flex-direction: column;
            });

            .time {
              font-size: 40px;
              font-weight: 500;

              .screenMobile({
                font-size: 28px;
              });
            }

            .date {
              font-size: 14px;
              color: #101010; // gray-5
              padding: 0 10px;

              .screenMobile({
                font-size: 12px;
                padding: 0 ;
              });
            }
          }

          .arrival {
            .screenMobile({
              flex-direction: column-reverse;
            });
          }

          .duration {
            flex: 1;
            display: flex;
            align-items: center;
            margin: 0 20px;

            .screenMobile({
              margin: 0 5px;
            });

            .divider {
              width: 100%;
              height: 1px;
              background-color: #dbd7d4; // gray-1
              margin: 0 10px;
            }

            .duration-info {
              display: flex;
              flex-direction: column;
              align-items: center;
              white-space: nowrap;
              gap: 2px;

              font-size: 14px;
              color: @sub-4;

              .screenMobile({
                gap: 0;

                font-size: 12px;
              });

              .duration-value {
                background-color: #f8f1e5; // sub-1
                color: #4f3d1e; // sub-4
                padding: 2px 6px;
                border-radius: 8px;
                font-size: 16px;
                white-space: nowrap;

                .screenMobile({
                  font-size: 12px;
                });
              }
            }
          }
        }

        .airport-details {
          display: flex;
          justify-content: space-between;
          margin-bottom: 6px;
          font-size: 16px;
          color: @gray-5;
          gap: 20px;

          .screenMobile({
            font-size: 12px;
          });
        }

        .flight-meal-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 6px;

          .flight-info {
            display: flex;
            align-items: center;
            gap: 30px;

            .screenMobile({
              gap: 10px;
            });

            .flight-number {
              display: flex;
              align-items: center;
              gap: 10px;

              font-size: 16px;
              color: @gray-5;

              .screenMobile({
                font-size: 12px;
              });

              .icon-plane {
                display: inline-block;
                width: 15px;
                height: 16px;
                background-color: #101010; // gray-5
              }
            }

            .aircraft-type {
              font-size: 16px;
              color: @sub-4;

              .screenMobile({
                font-size: 12px;
              });
            }

            .shared-flight {
              background-color: @red-1;
              color: #4f3d1e; // sub-4
              padding: 1px 4px;
              border-radius: 8px;

              font-size: 16px;
              color: @sub-4;

              .screenMobile({
                font-size: 12px;
              });
            }
          }

          .meal-info {
            display: flex;
            align-items: center;
            gap: 20px;

            font-size: 16px;
            color: @gray-4;

            .screenMobile({
              font-size: 12px;
              gap: 5px;
            });

            .screenMobile({
              flex-direction: column;
            });

            .flight-details-link {
              font-size: 16px;
              color: #942531;
              text-decoration: underline;
              cursor: pointer;
              display: flex;
              align-items: center;

              .screenMobile({
                font-size: 12px;
              });

              .icon {
                width: 16px;
                height: 16px;

                .screenMobile({
                  width: 12px;
                  height: 12px;
                });
              }

              .arrow-icon {
                background-image: url('../images/flightOptions/arrow-right.svg');
                background-size: contain;
                background-repeat: no-repeat;
                margin-left: 10px;
                transform: rotate(-90deg);
              }
            }
          }
        }

        .carrier-info {
          font-size: 16px;
          color: @gray-4;

          .screenMobile({
            font-size: 12px;
          });
        }
      }

      /* Transfer information bar */
      .transfer-info-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        .transfer-line {
          flex: 1;
          height: 1px;
          background-color: @gray-3; /* token: 中性色/gray-3 */

          &.left {
            margin-left: 20px;

            .screenMobile({
              margin-left: 10px;
            });
          }

          &.right {
            margin-right: 20px;

            .screenMobile({
              margin-right: 10px;
            });
          }
        }

        .transfer-info {
          display: flex;
          align-items: center;
          background-color: @gray-3; /* token: 中性色/gray-3 */
          border-radius: 8px;
          padding: 2px 20px;

          .transfer-text {
            font-size: 16px; /* token: 16px/16px-默认 */
            font-weight: 400;
            color: @gray-0; /* token: 中性色/gray-0 */
            line-height: auto;
            white-space: nowrap;

            .screenMobile({
              font-size: 12px;
            });
          }
        }
      }

      .info-cards {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        padding: 0 20px;
        gap: 20px;

        .screenMobile({
          gap: 10px;
          padding: 0 10px;
          margin: 10px 0 15px;
        });

        .info-card {
          flex: 1;
          background: #ffffff;
          box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
          border-radius: 8px;

          .card-header {
            background-color: #f4f6f9;
            padding: 10px 0;
            text-align: center;
            font-size: 16px;
            color: #3d3d3d;
            border-radius: 8px 8px 0 0;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 21px;
              height: 3px;
              background-color: #cc0100; // brand-1
              border-radius: 0 0 8px 8px;
            }

            .screenMobile({
              font-size: 12px;
            });
          }

          .card-content {
            padding: 10px 0;
            text-align: center;
            font-size: 16px;
            color: #2c2420; // gray-4

            .screenMobile({
              font-size: 12px;
            });
          }
        }
      }

      .notes-section {
        background-color: #fcf1d2;
        padding: 10px 20px;
        margin: 0 20px 20px;
        border: 1px solid rgba(238, 183, 28, 0.6);

        .screenMobile({
          padding: 10px;
          margin: 0 10px 10px;
        });

        .notes-header {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px;
          color: #101010; // gray-5

          .screenMobile({
            font-size: 14px;
          });
        }

        .notes-content {
          font-size: 14px;
          color: #101010; // gray-5

          .screenMobile({
            font-size: 12px;
          });
        }
      }

      .policy-header {
        font-size: 16px;
        font-weight: 500;
        color: @gray-5;
        margin: 0 20px 20px;

        .screenMobile({
          margin: 0 10px 10px;
        });
      }

      .policy-tables {
        display: flex;
        gap: 20px;
        margin: 0 20px 20px;

        .screenMobile({
          flex-direction: column;
          margin: 0 10px 10px;
          gap: 0px;
        });

        .policy-table {
          flex: 1;
          background: #ffffff;
          box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
          border-radius: 8px;

          .table-header {
            background-color: #f4f6f9;
            padding: 10px 0;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: #3d3d3d;
            border-radius: 8px 8px 0 0;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 21px;
              height: 3px;
              background-color: #cc0100; // brand-1
              border-radius: 0 0 8px 8px;
            }
          }

          .table-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 20px;
            font-size: 14px;
            color: #101010; // gray-5

            &.header-row {
              background-color: #ffffff;
              font-size: 16px;
              font-weight: 500;

              .screenMobile({
                font-size: 14px;
              });
            }

            .value {
              color: #c47330; // orange-3
            }
          }
        }
      }

      .footer-notes {
        font-size: 14px;
        color: #101010; // gray-5
        background-color: #ffffff;
        border-radius: 0 0 8px 8px;
        margin: 0 20px 20px;

        .screenMobile({
          font-size: 12px;
          margin: 0 10px 10px;
        });
      }
    }

    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 20px;
      margin-top: 30px;

      .screenMobile({
        flex-direction: column;
        gap: 10px;
      });

      .btn {
        width: 230px;
        height: 52px;
        border-radius: 8px;
        font-size: 20px;
        cursor: pointer;

        .screenMobile({
          width: 100%;
        });

        &.secondary {
          background: transparent;
          border: 1px solid #cc0100; // brand-1
          color: #cc0100; // brand-1
        }

        &.primary {
          background: #cc0100; // brand-1
          border: none;
          color: #ffffff; // gray-0
        }
      }
    }
  }

  &-right {
    margin-left: 20px;

    .screenPad({
      display: none;
    });

    .screenMobile({
      display: none;
    });
  }
}
