@import '../../less/variables.less';
@import '../../less/mediaMixin.less';

@import './expand-card/index.less';
@import './seat/index.less';

.as {
  .wrap-payment-top-tip {
    background: #fdf7e8;
    border: 1px solid #eeb71c;
    border-radius: 8px;
    padding: 20px;
    margin: 30px 0;

    .screenMobile({
      padding: 10px;
    });

    .payment-top-tip {
      max-width: none;

      .screenPad({
        padding: 0;
      });

      .clock-icon {
        width: 30px;
        height: 30px;
      }

      .main-message {
        margin-left: 8px;

        font-size: 24px;
        font-weight: 400;

        .screenMobile({
          font-size: 16px;
        });
      }

      .banner-content .message-area p {
        font-size: 14px;
        font-weight: 400;

        .screenMobile({
          font-size: 12px;
        });
      }
    }
  }
}

.additional-services-container {
  .additional-services-tag {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin: 20px 0;

    .screenMobile({
      gap: 10px;
    });

    .service-option {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 24px 0px;
      border-radius: 8px;
      border: 1px solid #ccc;
      opacity: 0.8;
      background: #fff;
      position: relative;
      cursor: pointer;

      .screenMobile({
          padding: 12px 0px;
        });

      &.active {
        opacity: 1;
        border: 2px solid #cc0100;
        background: linear-gradient(180deg, #cc0100 0%, rgba(204, 1, 0, 0.5) 100%);
        background-color: #cc0100;

        .service-content {
          .service-text {
            color: #fff;
          }

          .seat-icon {
            background-image: url('../../images/additionalServices/seat.svg');
          }

          .baggage-icon {
            background-image: url('../../images/additionalServices/baggage.svg');
          }

          .insurance-icon {
            background-image: url('../../images/additionalServices/insurance.svg');
          }
        }
      }

      .service-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20px;

        .screenPad({
        });

        .icon-container {
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;

          .screenMobile({
            width: 40px;
            height: 40px;
          });
        }

        .seat-icon {
          width: 100%;
          height: 100%;
          background-image: url('../../images/additionalServices/seat-1.svg');
          background-size: contain;
        }

        .baggage-icon {
          width: 100%;
          height: 100%;
          background-image: url('../../images/additionalServices/baggage-1.svg');
          background-size: contain;
        }

        .insurance-icon {
          width: 100%;
          height: 100%;
          background-image: url('../../images/additionalServices/insurance-1.svg');
          background-size: contain;
        }

        .service-text {
          font-size: 20px;
          font-weight: 500;
          color: #2c2420;
          text-align: center;

          .screenMobile({
            font-size: 14px;
          });
        }
      }

      .service-status {
        position: absolute;
        top: 10px;
        right: 10px;

        .status-content {
          display: flex;
          align-items: center;
          gap: 4px;

          .check-icon {
            width: 16px;
            height: 16px;
          }

          .status-text {
            font-size: 14px;
            font-weight: 500;
            color: #46a716;

            .screenMobile({
              display: none;
            });
          }
        }
      }
    }
  }

  .additional-services-seat-card,
  .additional-services-baggage-card,
  .additional-services-insurance-card {
    display: none;
    margin-top: 20px;

    &.show {
      display: block;
    }
  }

  .additional-services-seat-card.show {
    display: block;
  }
}
