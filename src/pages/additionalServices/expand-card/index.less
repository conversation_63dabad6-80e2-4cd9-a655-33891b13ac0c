@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.expand-card {
  .flight-info-card {
    width: 100%;
    border: 1px solid @gray-2;
    border-top: none;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transition: height 0.3s ease;

    .card-header {
      width: 100%;
      height: 6px;
      background: @red-2;
      border-radius: 8px 8px 0 0;
    }

    .card-content {
      background: linear-gradient(180deg, #ffeded 0%, rgba(255, 237, 237, 0) 100%);
      padding: 22px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: all 0.3s ease;

      .screenPad({
        padding: 8px 20px;
      });

      .screenMobile({
        padding: 8px 10px;
      });

      .flight-details {
        display: flex;
        align-items: center;
        gap: 20px;
        flex: 1;

        .screenPad({
          flex-direction: column;
          align-items: normal;
          gap: 4px;
        });

        .screenMobile({
          flex-direction: column;
          align-items: normal;
          gap: 4px;
        });

        .journey-badge {
          width: max-content;
          background: @orange-3;
          color: @gray-1_1;
          padding: 0 7px;
          height: 20px;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .flight-info-and-date {
          display: flex;
          align-items: center;
          gap: 20px;

          .screenMobile({
            flex-direction: column;
            align-items: normal;
            gap: 4px;
          });
        }

        .route-info {
          display: flex;
          align-items: center;
          gap: 20px;

          .departure-city,
          .arrival-city {
            font-size: 20px;
            font-weight: 400;
            color: @gray-5; /* 中性色/gray-5 */

            .screenMobile({
              font-size: 14px;
            });
          }

          .route-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .flight-date {
          font-size: 16px;
          font-weight: 400;
          color: @gray-3; /* 中性色/gray-3 */

          .screenMobile({
            font-size: 14px;
          });
        }

        .changes-info-and-status-info {
          display: flex;
          align-items: center;
          gap: 20px;

          .screenMobile({
            gap: 10px;
          });
        }

        .changes-info {
          display: flex;
          align-items: center;
          gap: 4px;

          .changes-text {
            font-size: 16px;
            font-weight: 400;
            color: @gray-3; /* 中性色/gray-3 */

            .screenMobile({
              font-size: 14px;
            });
          }

          .info-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }

        .status-info {
          display: flex;
          // display: none;
          align-items: center;
          gap: 4px;

          .status-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
          }

          .status-text {
            font-size: 16px;
            font-weight: 400;
            color: #46a716; /* 绿色 */

            .screenMobile({
              font-size: 14px;
            });
          }

          // &.status-selected {
          //   display: flex;
          // }
        }
      }

      .expand-control {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        user-select: none;

        .screenMobile({
          margin-left: -10px;
        });

        .expand-text {
          font-size: 16px;
          font-weight: 400;
          color: @orange-3; /* 状态色/orange-3 */
          line-height: 23px;
        }

        .expand-icon {
          transition: transform 0.3s ease;

          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 14px;
        }
      }
    }

    .expand-content {
      padding: 0 20px;

      .screenMobile({
        padding: 0 10px;
      });
    }

    // 展开状态
    &.expanded {
    }
  }
}
