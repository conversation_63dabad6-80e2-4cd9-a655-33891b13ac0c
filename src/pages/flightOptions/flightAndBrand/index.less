@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.flight-and-brand {
  .notes {
    padding: 20px;
    margin: 20px 0;
    background: #fdf7e8;
    border: 1px solid #eeb71c;

    font-size: 14px;
    color: @gray-5;

    .screenMobile({
      margin: 10px 0;
      padding: 10px;

      font-size: 12px;
    });

    &-title {
      font-size: 16px;
      font-weight: 500;
      color: @gray-5;

      .screenMobile({
        font-size: 14px;
      });
    }

    &-text {
      margin-top: 10px;

      font-size: 14px;
      color: @gray-5;

      .screenMobile({
        font-size: 12px;
      });
    }
  }

  &-sift {
    display: flex;
    align-items: center;

    &-item {
      display: flex;
      align-items: center;
      margin-right: 20px;

      font-size: 16px;
      color: @gray-5;

      .screenMobile({
        font-size: 12px;
      });

      &-img {
        margin-left: 10px;
        width: 16px;
        height: 16px;

        .screenMobile({
          width: 12px;
          height: 12px;
        });
      }
    }
  }

  &-list {
    margin-top: 30px;

    .screenMobile({
      margin-top: 20px;
    });

    .flight-and-brand-item {
      margin-bottom: 20px;

      &.expanded {
        .flight-card {
          border-radius: 8px 8px 0 0;
          border-width: 1px 1px 0;
        }
      }

      /* 航班卡片样式 */
      .flight-card {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
        display: flex;
        border: 1px solid #cccccc;

        .screenPad({
          flex-direction: column;
        });

        .screenMobile({
          flex-direction: column;
          border-radius: 4px;
        });
      }

      /* 舱位选择样式 */
      .cabin-options {
        display: flex;

        .cabin-option {
          min-width: 182px;
          flex: 1;
          display: flex;
          flex-direction: column;
          cursor: pointer; /* 添加指针样式表明可点击 */
          position: relative; /* 为添加选中指示器做准备 */

          border: 1px solid #cccccc;
          border-width: 0 0 0 1px;

          /* 选中状态样式 */
          &.active .cabin-price {
            background-color: #f4f6f9 !important;
            transition: background-color 0.3s ease;
          }

          .screenPad({
            &:nth-child(1){
              border-width: 0;
            }
          });

          .screenMobile({
            min-width: auto;

            &:nth-child(1){
              border-width: 0;
            }
          });
        }

        .cabin-option.economy .cabin-header {
          background-color: #e64343;
        }

        .cabin-option.premium-economy .cabin-header {
          background-color: #ebddba;
        }

        .cabin-option.business .cabin-header {
          background-color: #4c5560;
        }

        .cabin-header {
          width: 100%;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 6px 2px;

          .screenMobile({
            height: 24px;
            padding: 0;
          });
        }

        .cabin-icon-1 {
          width: 20px;
          height: 20px;
          background-image: url('../images/flightOptions/seat-icon.svg');
          background-size: contain;
          background-repeat: no-repeat;

          .screenMobile({
           display: none;
          });
        }

        .cabin-name {
          font-size: 16px;
          font-weight: 500;
          color: #ffffff;
          margin-left: 3px;

          .screenMobile({
            font-size: 12px;
            font-weight: 400;
            margin-left: 0px;
            white-space: nowrap;
          });
        }

        .cabin-price {
          width: 100%;
          flex: 1;
          background-color: #ffffff;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .screenPad({
            flex-direction: row;
            padding: 24px 0;
          });

          .screenMobile({
            flex-direction: row;
            padding: 10px 0;
          });
        }

        .price-currency {
          font-size: 16px;
          color: #4f3d1e;

          .screenMobile({
            font-size: 12px;
            font-weight: 400;
          });
        }

        .price-amount {
          font-size: 28px;
          font-weight: 500;
          color: #cc0100;
          margin-top: 8px;

          .screenPad({
            margin: 0 8px;
           });

          .screenMobile({
            font-size: 16px;
            margin: 0 8px;
          });
        }

        .price-note {
          font-size: 16px;
          color: #4f3d1e;
          margin-top: 8px;

          .screenPad({
            margin-top: 0;
          });

          .screenMobile({
            display: none;
          });
        }
      }

      .cabin-options-multi {
        border: 1px solid #ccc;
        border-width: 0 0 0 1px;

        .cabin-name {
          font-size: 16px;
          font-weight: 500;
          color: #ffffff;
          margin-left: 3px;

          .screenMobile({
            font-size: 12px;
            font-weight: 400;
            margin-left: 0px;
            white-space: nowrap;
          });
        }

        .cabin-option-multi {
          min-width: 250px;
          height: 100%;
          // border: 1px solid @brand-1;
          // border-width: 6px 0 0 0;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          transition: background-color 0.3s ease;

          /* 选中状态样式 */
          &.active {
            background-color: #f4f6f9;

            .arrow {
              transform: rotate(180deg);
            }
          }

          .unit {
            margin-top: 10px;

            font-size: 16px;
            color: @gray-5;
          }

          .amount {
            margin-top: 10px;

            font-size: 28px;
            font-weight: 500;
            color: @brand-1;
          }

          .arrow {
            margin-top: 20px;
            user-select: none;
          }
        }

        .cabin-option-multi.economy .cabin-header {
          background-color: #e64343;
        }

        .cabin-option-multi.premium-economy .cabin-header {
          background-color: #ebddba;
        }

        .cabin-option-multi.business .cabin-header {
          background-color: #4c5560;
        }

        .cabin-header {
          width: 100%;
          height: 50px;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 6px 2px;

          .screenMobile({
            height: 24px;
            padding: 0;
          });
        }

        .cabin-icon-1 {
          width: 20px;
          height: 20px;
          background-image: url('../images/flightOptions/seat-icon.svg');
          background-size: contain;
          background-repeat: no-repeat;

          .screenMobile({
           display: none;
          });
        }
      }

      /* 品牌展开部分样式 */
      .brand-expanded {
        display: none; /* 默认隐藏品牌展开部分 */

        &.active {
          display: block; /* 当添加active类时显示 */
        }

        // 品牌变化
        &.active.economy {
          .price-component {
            .price-header {
              background-image: url('../images/flightOptions/economy.svg');
            }

            .price-name {
              color: @gray-0;
            }

            .price-footer {
              background-color: #e64343;
            }

            .book-icon {
              background-image: url('../images/flightOptions/book-icon-f.svg');
            }

            .book-button span {
              color: @gray-0;
            }
          }
        }

        &.active.premium-economy {
          .price-component {
            .price-header {
              background-image: url('../images/flightOptions/premium-economy.svg');
            }

            .price-footer {
              background-color: #ebddba;
            }
          }
        }

        &.active.business {
          .price-component {
            .price-header {
              background-image: url('../images/flightOptions/business.svg');
            }

            .price-name {
              color: @gray-0;
            }

            .price-footer {
              background-color: #4c5560;
            }

            .book-icon {
              background-image: url('../images/flightOptions/book-icon-f.svg');
            }

            .book-button span {
              color: @gray-0;
            }
          }
        }

        /* 多段航班品牌展开内容 */
        .brand-content-multi {
          display: flex;
          background-color: #f4f6f9; /* 中性色/gray-0 */
          border: 1px solid #cccccc;
          border-width: 0 1px 1px;
          border-radius: 0 0 8px 8px;
          padding: 20px;

          .screenMobile({
            padding: 0;

            flex-direction: column;
          });

          .left-content {
            flex: 1;
            padding: 30px;
            background: @gray-0;

            .screenMobile({
              padding: 10px;
            });
          }

          .price-summary {
            .price-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 6px;

              .price-left {
                display: flex;
                flex-direction: column;
                gap: 6px;

                .price-label {
                  font-size: 16px; /* 16px/16px-默认 */
                  color: #554e49; /* 中性色/gray-3 */
                }

                .price-help {
                  display: flex;
                  align-items: center;
                  gap: 6px;

                  .price-label-sub {
                    font-size: 16px; /* 16px/16px-默认 */
                    color: #554e49; /* 中性色/gray-3 */
                  }
                }
              }

              .price-values {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 6px;

                .price-main {
                  font-size: 16px; /* 16px/16px-默认 */
                  color: #554e49; /* 中性色/gray-3 */
                }

                .price-sub {
                  font-size: 16px; /* 16px/16px-默认 */
                  color: #554e49; /* 中性色/gray-3 */
                }
              }
            }
          }

          .divider {
            height: 1px;
            background-color: #dbd7d4; /* 中性色/gray-1 */
            margin: 10px 0;
          }

          .flight-details-expanded {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .detail-item {
              background-color: rgba(248, 241, 229, 0.3); /* 辅色/sub-1透明度30 */
              border-radius: 8px;
              display: flex;
              align-items: center;
              padding: 6px;
              gap: 2px;

              .detail-icon {
                width: 18px;
                height: 18px;
                background-size: contain;
                background-repeat: no-repeat;
                flex-shrink: 0;

                &.cabin-class-icon {
                  background-image: url('../images/flightOptions/cabin-icon.svg');
                }

                &.baggage-icon {
                  background-image: url('../images/flightOptions/baggage-icon.svg');
                }

                &.refund-icon {
                  background-image: url('../images/flightOptions/refund-icon.svg');
                }

                &.change-icon {
                  background-image: url('../images/flightOptions/change-icon.svg');
                }

                &.mileage-icon {
                  background-image: url('../images/flightOptions/mileage-icon.svg');
                }

                &.bonus-icon {
                  background-image: url('../images/flightOptions/bonus-icon.svg');
                }
              }

              .detail-label {
                font-size: 16px; /* 16px/16px-默认 */
                color: @gray-3; /* 中性色/gray-3 */
                flex: 1;
                margin-left: 2px;
              }

              .detail-value {
                font-size: 16px; /* 16px/16px-默认 */
                text-align: right;

                &.cabin-type {
                  color: #942531; /* 品牌色/brand-2 */
                }

                &.baggage-link {
                  color: #942531; /* 品牌色/brand-2 */
                  text-decoration: underline;
                }

                &.refund-fee,
                &.change-fee {
                  color: #942531; /* 品牌色/brand-2 */
                }

                &.mileage-points,
                &.bonus-points {
                  color: #554e49; /* 中性色/gray-3 */
                }
              }
            }
          }
        }

        .booking-section {
          width: 228px;
          display: flex;
          align-items: center;
          justify-content: center;

          .screenMobile({
            width: auto;
          });

          .booking-card {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
            background-color: #e64343; /* 品牌色/brand-4(购票) */
            border-radius: 0 8px 8px 0;

            .screenMobile({
              padding: 10px;
              border-radius: 0;
              gap: 10px;
            });

            .booking-price {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4px;

              .price-currency {
                font-size: 16px; /* 16px/16px-默认 */
                color: #ffffff; /* 中性色/gray-0 */
              }

              .price-amount {
                font-size: 30px; /* 30px-粗 */
                font-weight: 500;
                color: #ffffff; /* 中性色/gray-0 */

                .screenMobile({
                  font-size: 24px;
                  font-weight: 400;
                });
              }
            }

            .price-note {
              font-size: 16px; /* 16px/16px-默认 */
              color: #ffffff; /* 中性色/gray-0 */
            }

            .book-button {
              display: flex;
              align-items: center;
              gap: 6px;
              background: none;
              border: none;
              cursor: pointer;

              .book-icon {
                width: 16px;
                height: 16px;
                background-image: url('../images/flightOptions/book-icon-f.svg');
                background-size: contain;
                background-repeat: no-repeat;
              }

              span {
                font-size: 20px; /* font_23:8266 */
                font-weight: 500;
                color: #ffffff; /* 中性色/gray-0 */
              }
            }
          }
        }

        /* 根据舱位类型调整预订区域颜色 */
        &.active.economy .booking-section .booking-card {
          background-color: #e64343; /* 品牌色/brand-4(购票) */
        }

        &.active.premium-economy .booking-section .booking-card {
          background-color: #ebddba;

          .booking-price .price-currency,
          .booking-price .price-amount,
          .price-note,
          .book-button span {
            color: #95490b;
          }

          .book-button .book-icon {
            background-image: url('../images/flightOptions/book-icon.svg');
          }
        }

        &.active.business .booking-section .booking-card {
          background-color: #4c5560;
        }

        .brand-options {
          display: flex;
          gap: 20px;
          padding: 20px;
          background-color: #f4f6f9;
          border: 1px solid #cccccc;
          border-width: 0 1px 1px;
          border-radius: 0 0 8px 8px;

          overflow-y: auto;

          // 默认隐藏所有舱位区域
          &.economy-section,
          &.premium-economy-section,
          &.business-section {
            display: none;
            flex-direction: column;
            gap: 0;
            padding: 0;

            .price-options-grid {
              display: flex;
              gap: 20px;
              padding: 20px;
              overflow-x: auto;
            }

            // 当对应的舱位被选中时显示
            &.active {
              display: flex;
            }
          }

          // 经济舱区域样式
          &.economy-section {
          }

          // 超级经济舱区域样式
          &.premium-economy-section {
          }

          // 商务舱区域样式
          &.business-section {
          }
        }

        /* 价格组件样式 */
        .price-component {
          width: 320px;
          display: flex;
          flex-direction: column;
          flex-shrink: 0;

          .screenMobile({
            width: 276px;
          });

          .price-tag {
            height: 27px;
            background-color: #fdf7e8;
            border: 1px solid #ebddba;
            border-width: 1px 1px 0;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            color: #95490b;
            margin: 0 auto;
            padding: 2px 14px;

            .screenMobile({
              font-size: 12px;
            });
          }

          .price-header {
            width: 100%;
            height: 76px;
            background-color: @gray-0;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;

            background-image: url('../images/flightOptions/premium-economy.svg');
            background-size: 100%;
            background-repeat: no-repeat;

            .screenMobile({
            });
          }

          .price-name {
            font-size: 20px;
            font-weight: 500;
            color: #95490b;
            text-align: center;
            z-index: 1;
            padding: 0 10px;

            .screenMobile({
              font-size: 16px;
            });
          }

          .price-content {
            background-color: #ffffff;
            padding: 30px 20px 10px;
            flex: 1;

            .screenMobile({
             padding: 10px 20px 10px;
            });
          }

          .price-display {
            display: flex;
            justify-content: center;
            align-items: flex-end;
            gap: 4px;

            .price-currency {
              align-self: flex-end;
              color: #554e49;
              font-size: 16px;

              .screenMobile({
                font-size: 12px;
              });
            }

            .price-amount {
              font-size: 30px;
              font-weight: 500;
              color: #cc0100;

              .screenMobile({
                font-size: 28px;
              });
            }
          }

          .price-details {
            margin-top: 30px;
          }

          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
          }

          .detail-label {
            font-size: 16px;
            color: #554e49;
            display: flex;
            align-items: center;

            .screenMobile({
              font-size: 12px;
            });

            .tips {
              margin-left: 4px;
              color: @gray-3;

              .screenMobile({
               & .icon-zh-ask{
                  font-size: 12px;
               }
              });
            }
          }

          .detail-value {
            font-size: 16px;
            color: #554e49;
            text-align: right;

            .screenMobile({
              font-size: 12px;
            });
          }

          .detail-divider {
            height: 1px;
            background-color: #dbd7d4;
            margin: 10px 0;
          }

          .detail-features {
            display: flex;
            flex-direction: column;
            gap: 10px;
          }

          .feature-item {
            background-color: rgba(248, 241, 229, 0.3);
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px;
          }

          .feature-icon {
            width: 18px;
            height: 18px;
            background-size: contain;
            background-repeat: no-repeat;

            .screenMobile({
              width: 12px;
              height: 12px;
            });
          }

          .cabin-icon {
            background-image: url('../images/flightOptions/cabin-icon.svg');
          }

          .baggage-icon {
            background-image: url('../images/flightOptions/baggage-icon.svg');
          }

          .refund-icon {
            background-image: url('../images/flightOptions/refund-icon.svg');
          }

          .change-icon {
            background-image: url('../images/flightOptions/change-icon.svg');
          }

          .mileage-icon {
            background-image: url('../images/flightOptions/mileage-icon.svg');
          }

          .bonus-icon {
            background-image: url('../images/flightOptions/bonus-icon.svg');
          }

          .feature-name {
            font-size: 16px;
            color: #554e49;
            margin-left: 2px;
            flex: 1;

            .screenMobile({
              font-size: 12px;
            });
          }

          .feature-value {
            font-size: 16px;
            color: @gray-3;
            text-align: right;

            .screenMobile({
              font-size: 12px;
            });

            &-hover {
              color: @brand-2;
            }

            &-link {
              color: @brand-2;
              text-decoration: underline;
            }
          }

          .price-footer {
            background-color: #ebddba;
            border-radius: 0 0 8px 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 12px 0;

            .screenMobile({
              padding: 8px 0;
            });
          }

          .book-button {
            display: flex;
            align-items: center;
            gap: 6px;
            background: none;
            border: none;
            cursor: pointer;
          }

          .book-icon {
            width: 16px;
            height: 16px;
            background-image: url('../images/flightOptions/book-icon.svg');
            background-size: contain;
            background-repeat: no-repeat;

            .screenMobile({
              width: 12px;
              height: 12px;
            });
          }

          .book-button span {
            font-size: 20px;
            font-weight: 500;
            color: #95490b;

            .screenMobile({
              font-size: 14px;
            });
          }
        }
      }
    }
  }

  .transfer-bar {
    display: flex;
    align-items: center;
    height: 27px;
    width: 100%;
    margin: 30px 0;

    .transfer-line-left,
    .transfer-line-right {
      flex: 1;
      height: 1px;
      background-color: @gray-3;
    }

    .transfer-info {
      background-color: @gray-3;
      border-radius: 8px;
      padding: 2px 20px;
      display: flex;
      justify-content: center;
      align-items: center;

      span {
        font-size: 16px;
        color: @gray-0;
        white-space: nowrap;
      }
    }
  }

  .flight-info {
    flex: 1;
    background-color: @gray-0;
    padding: 14px 24px;

    .screenMobile({
         padding: 20px 10px;
    });

    .flight-info-header {
      display: flex;
      flex-direction: column;
    }

    .departure-time,
    .arrival-time {
      font-size: 40px;
      font-weight: 500;
      color: #101010;

      .screenMobile({
        font-size: 20px;
      });
    }

    .arrival-time {
      position: relative;

      .add1 {
        position: absolute;
        top: -30%;
        right: 0;

        font-size: 16px;
        font-weight: 400;
        color: @gray-5;

        .screenMobile({
          top: -50%;

          font-size: 12px;
        });
      }
    }

    .flight-duration {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .duration-tag {
      background-color: #fdf7e8;
      border-radius: 8px;
      padding: 1px 6px;
      font-size: 20px;
      color: #4f3d1e;

      .screenMobile({
        font-size: 12px;
      });
    }

    .flight-route {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .flight-place {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40px;

      .screenMobile({
        margin-bottom: 15px;
      });

      .departure-airport,
      .arrival-airport {
        font-size: 16px;
        color: #101010;

        .screenMobile({
          font-size: 12px;
        });
      }

      .departure-airport {
        margin-right: 10px;
      }

      .arrival-airport {
        margin-left: 10px;
      }
    }

    .arrival-airport {
      text-align: right;
    }

    .flight-route-line {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .line-left,
    .line-right {
      flex: 1;
      height: 1px;
      background-color: #554e49;
      margin: 0 20px;
    }

    .transfer-card,
    .stop-card {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .transfer-city,
    .stop-city {
      background-color: #ffffff;
      border-radius: 8px;
      padding: 0 10px;
      font-size: 20px;
      color: #4f3d1e;
      text-align: center;

      .screenPad({
        font-size: 16px;
      });

      .screenMobile({
        font-size: 12px;
      });

      img {
        width: 16px;
        height: 16px;

        .screenMobile({
              width: 12px;
              height: 12px;
            });
      }
    }

    .stop-city {
      color: @sub-4;
    }

    .flight-details {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      &-left {
        display: flex;
        flex-direction: column;
        align-items: center;

        &-content {
          display: flex;
          align-items: center;

          &:nth-child(n + 2) {
            margin-top: 10px;

            .screenMobile({
                  margin-top: 6px;
                });
          }

          .flight-number {
            display: flex;
            align-items: center;
            margin-right: 10px;
          }

          .aircraft-type {
            font-size: 16px;
            color: #4f3d1e;
            margin-right: 10px;

            .screenMobile({
                  font-size: 12px;
                });
          }

          .flight-tags {
            display: flex;
          }

          .airline-logo {
            width: 16px;
            height: 16px;
            margin-right: 2px;
            background-size: contain;
            background-repeat: no-repeat;

            .screenMobile({
              width: 12px;
              height: 12px;
            });
          }

          .flight-code {
            font-size: 16px;
            color: #101010;

            .screenMobile({
                  font-size: 12px;
                });
          }

          .tag-shared {
            background-color: #fff3ed;
            border-radius: 8px;
            padding: 0 4px;
            font-size: 16px;
            color: #4f3d1e;

            .screenMobile({
                  font-size: 12px;
                });
          }
        }
      }
    }

    .flight-details-link {
      font-size: 16px;
      color: #942531;
      text-decoration: underline;
      cursor: pointer;
      display: flex;
      align-items: center;

      .screenMobile({
            font-size: 12px;
          });

      .icon {
        width: 16px;
        height: 16px;

        .screenMobile({
              width: 12px;
              height: 12px;
            });
      }

      .arrow-icon {
        background-image: url('../images/flightOptions/arrow-right.svg');
        background-size: contain;
        background-repeat: no-repeat;
        margin-left: 10px;
        transform: rotate(-90deg);
      }
    }

    &.modal {
      padding: 18px 10px;
      border: 1px solid #cccccc;
      border-radius: 8px;

      .screenMobile({
        padding: 0;
        border: none;
      });

      .duration-tag {
        font-size: 14px;
      }

      .stop-city {
        font-size: 14px;
      }

      .flight-place {
        margin-bottom: 20px;
      }
    }
  }
}

// 多段
.flight-and-brand.multi {
  .flight-card {
    .screenPad({
      flex-direction: row;
    });
  }

  .flight-place {
    .screenPad({
      margin-bottom: 30px;
    });
  }

  .cabin-option-multi {
    .screenMobile({
      width: 100%;
      padding-bottom: 10px;
    });

    .unit {
      .screenMobile({
        margin-top: 10px !important;
      });
    }

    // .amount {
    //   margin-top: 10px;

    //   font-size: 28px;
    //   font-weight: 500;
    //   color: @brand-1;
    // }

    .arrow {
      .screenMobile({
        margin-top: 10px !important;
      });
    }
  }
}
