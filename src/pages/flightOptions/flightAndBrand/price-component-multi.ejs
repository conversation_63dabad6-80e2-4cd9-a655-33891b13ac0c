<%
    const fareTableJSON = JSON.stringify({
      showHeader: false,
      bordered: false,
      columns: [
        {
          title: '',
          dataIndex: 'name',
          align: 'left',
        },
        {
          title: '',
          dataIndex: 'amount',
          align: 'right',
        },
      ],
      dataSource: [
        {
          name: 'Taxes and Fees',
          amount: 'CNY 8',
        },
        {
          name: 'Fare',
          amount: 'CNY 18',
        },
        {
          name: 'Surcharge',
          amount: 'CNY 34',
        },
        {
          name: 'Service fee',
          amount: 'CNY 53',
        },
        {
          name: 'Taxes and Fees',
          amount: 'CNY 33',
        },
        {
          name: 'Taxes and Fees',
          amount: 'CNY 270',
        },
      ],
    });

    const UATableJSON = JSON.stringify({
      columns: [
        {
          title: 'Flight number',
          dataIndex: 'flightNumber',
        },
        {
          title: 'Flight Segment',
          dataIndex: 'flightSegment',
        },
        {
          title: 'Cabin Class and Brand',
          dataIndex: 'cabinClassAndBrand',
        },
      ],
      dataSource: [
        {
          flightNumber: 'ZH307',
          flightSegment: 'Beijing-Shenzhen',
          cabinClassAndBrand: 'U(Premium Economy)',
        },
        {
          flightNumber: 'ZH8034',
          flightSegment: 'Shenzhen-Beijing',
          cabinClassAndBrand: 'H(Premium Economy)',
        },
      ],
    });

    const refundTableJSON = JSON.stringify({
      columns: [
        {
          title: 'Change of Flight/Cabin Class',
          dataIndex: 'time',
          colSpan: 2,
          _onCellType: 'refund',
        },
        {
          title: '',
          dataIndex: 'isUse',
          colSpan: 0,
        },
        {
          title: 'Refund',
          dataIndex: 'amount',
        },
      ],
      dataSource: [
        {
          key: '1',
          time: 'After Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '2',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
        {
          key: '3',
          time: 'Before Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '4',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
      ],
      notes: [
        'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
        'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
      ],
    });

    const changeTableJSON = JSON.stringify({
      columns: [
        {
          title: 'Change of Flight/Cabin Class',
          dataIndex: 'time',
          colSpan: 2,
          _onCellType: 'change',
        },
        {
          title: '',
          dataIndex: 'isUse',
          colSpan: 0,
        },
        {
          title: 'change',
          dataIndex: 'amount',
        },
      ],
      dataSource: [
        {
          key: '1',
          time: 'After Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '2',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
        {
          key: '3',
          time: 'Before Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '4',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
      ],
      notes: [
        'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied. ',
        'For changes within the same cabin class, if there is a price difference between the fares, the fare difference must be made up. When both a rescheduling fee and an upgrade fee occur, they will be charged simultaneously.',
      ],
    });
%>

<!-- 多段航班品牌展开内容 -->
<div class="brand-content-multi" role="region" aria-label="Multi-segment flight brand content">
  <div class="left-content">
    <!-- 价格和税费信息 -->
    <div class="price-summary" role="group" aria-label="Price summary">
      <div class="price-row">
        <div class="price-left">
          <span class="price-label">Taxes and Fees</span>
          <div class="price-help">
            <span class="price-label-sub">Fare</span>
            <button
              role="button"
              tabindex="0"
              class="tips"
              aria-label="View fare breakdown details"
              data-tippy-table="<%= fareTableJSON %>">
              <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </button>
          </div>
        </div>
        <div class="price-values">
          <div class="price-main" aria-label="Main price CNY 7,099">CNY 7,099</div>
          <div class="price-sub" aria-label="Additional fees CNY 900">CNY 900</div>
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider" role="separator" aria-hidden="true"></div>

    <!-- 航班详细信息 -->
    <div class="flight-details-expanded" role="group" aria-label="Flight details">
      <!-- 舱位等级 -->
      <div class="detail-item">
        <div class="detail-icon cabin-class-icon" role="img" aria-label="Cabin class icon"></div>
        <span class="detail-label">Cabin Class</span>
        <button
          class="detail-value cabin-type"
          aria-label="Cabin class U+A, click for details"
          data-tippy-table="<%= UATableJSON %>">
          U+A
        </button>
      </div>

      <!-- 行李 -->
      <div class="detail-item">
        <div class="detail-icon baggage-icon" role="img" aria-label="Baggage icon"></div>
        <span class="detail-label">Baggage</span>
        <span class="detail-value baggage-link" aria-label="Baggage allowance 1 piece">1 Pieces</span>
      </div>

      <!-- 退票 -->
      <div class="detail-item">
        <div class="detail-icon refund-icon" role="img" aria-label="Refund icon"></div>
        <span class="detail-label">Refund</span>
        <button
          class="detail-value refund-fee"
          role="button"
          tabindex="0"
          aria-label="Refund fee 300 plus, click for details"
          data-tippy-table="<%= refundTableJSON %>">
          300+
        </button>
      </div>

      <!-- 改签 -->
      <div class="detail-item">
        <div class="detail-icon change-icon" role="img" aria-label="Change icon"></div>
        <span class="detail-label">Change</span>
        <button
          class="detail-value change-fee"
          role="button"
          tabindex="0"
          aria-label="Change fee 300 plus, click for details"
          data-tippy-table="<%= changeTableJSON %>">
          300+
        </button>
      </div>

      <!-- 基础里程 -->
      <div class="detail-item">
        <div class="detail-icon mileage-icon" role="img" aria-label="Mileage icon"></div>
        <span class="detail-label">Base Mileage</span>
        <span class="detail-value mileage-points" aria-label="Base mileage plus 1142 points">+1142</span>
      </div>

      <!-- 奖励里程 -->
      <div class="detail-item">
        <div class="detail-icon bonus-icon" role="img" aria-label="Bonus icon"></div>
        <span class="detail-label">Bonus Mileage</span>
        <span class="detail-value bonus-points" aria-label="Bonus mileage plus 1142 points">+1142</span>
      </div>
    </div>
  </div>

  <!-- 右侧预订按钮 -->
  <div class="booking-section" role="complementary" aria-label="Booking section">
    <div class="booking-card">
      <div class="booking-price" aria-label="Total price CNY 1,500">
        <div class="price-currency" aria-hidden="true">CNY</div>
        <div class="price-amount" aria-hidden="true">1,500</div>
      </div>
      <div class="price-note" aria-label="Tax included">含税</div>
      <button class="book-button" aria-label="Book this flight for CNY 1,500 including tax">
        <div class="book-icon" role="img" aria-label="Book icon" aria-hidden="true"></div>
        <span>BOOK</span>
      </button>
    </div>
  </div>
</div>
