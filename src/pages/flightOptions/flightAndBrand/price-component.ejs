<%
    const fareTableJSON = JSON.stringify({
      showHeader: false,
      bordered: false,
      columns: [
        {
          title: '',
          dataIndex: 'name',
          align: 'left',
        },
        {
          title: '',
          dataIndex: 'amount',
          align: 'right',
        },
      ],
      dataSource: [
        {
          name: 'Taxes and Fees',
          amount: 'CNY 8',
        },
        {
          name: 'Fare',
          amount: 'CNY 18',
        },
        {
          name: 'Surcharge',
          amount: 'CNY 34',
        },
        {
          name: 'Service fee',
          amount: 'CNY 53',
        },
        {
          name: 'Taxes and Fees',
          amount: 'CNY 33',
        },
        {
          name: 'Taxes and Fees',
          amount: 'CNY 270',
        },
      ],
    });

    const UATableJSON = JSON.stringify({
      columns: [
        {
          title: 'Flight number',
          dataIndex: 'flightNumber',
        },
        {
          title: 'Flight Segment',
          dataIndex: 'flightSegment',
        },
        {
          title: 'Cabin Class and Brand',
          dataIndex: 'cabinClassAndBrand',
        },
      ],
      dataSource: [
        {
          flightNumber: 'ZH307',
          flightSegment: 'Beijing-Shenzhen',
          cabinClassAndBrand: 'U(Premium Economy)',
        },
        {
          flightNumber: 'ZH8034',
          flightSegment: 'Shenzhen-Beijing',
          cabinClassAndBrand: 'H(Premium Economy)',
        },
      ],
    });

    const refundTableJSON = JSON.stringify({
      columns: [
        {
          title: 'Change of Flight/Cabin Class',
          dataIndex: 'time',
          colSpan: 2,
          _onCellType: 'refund',
        },
        {
          title: '',
          dataIndex: 'isUse',
          colSpan: 0,
        },
        {
          title: 'Refund',
          dataIndex: 'amount',
        },
      ],
      dataSource: [
        {
          key: '1',
          time: 'After Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '2',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
        {
          key: '3',
          time: 'Before Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '4',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
      ],
      notes: [
        'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
        'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
      ],
    });

    const changeTableJSON = JSON.stringify({
      columns: [
        {
          title: 'Change of Flight/Cabin Class',
          dataIndex: 'time',
          colSpan: 2,
          _onCellType: 'change',
        },
        {
          title: '',
          dataIndex: 'isUse',
          colSpan: 0,
        },
        {
          title: 'change',
          dataIndex: 'amount',
        },
      ],
      dataSource: [
        {
          key: '1',
          time: 'After Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '2',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
        {
          key: '3',
          time: 'Before Takeoff',
          isUse: 'All Unused',
          amount: 'CNY661.0',
        },
        {
          key: '4',
          time: '',
          isUse: 'Partially Used',
          amount: 'CNY661.0',
        },
      ],
      notes: [
        'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied. ',
        'For changes within the same cabin class, if there is a price difference between the fares, the fare difference must be made up. When both a rescheduling fee and an upgrade fee occur, they will be charged simultaneously.',
      ],
    });
%>

<div class="price-component" role="article" aria-label="Flight price option">
  <div class="price-tag" aria-label="2 seats remaining">2 Remaining</div>
  <div class="price-header">
    <div class="price-name" aria-label="Business Travel Economy Class">Business Travel Economy Class</div>
  </div>
  <div class="price-content">
    <div class="price-display" aria-label="Total price CNY 7,999">
      <div class="price-currency" aria-hidden="true">CNY</div>
      <div class="price-amount" aria-hidden="true">7,999</div>
    </div>

    <div class="price-details" role="group" aria-label="Price breakdown">
      <div class="detail-row">
        <div class="detail-label">
          <span>Taxes and Fees</span>
        </div>
        <div class="detail-value" aria-label="Taxes and fees CNY 7,099">CNY 7,099</div>
      </div>
      <div class="detail-row">
        <div class="detail-label">
          <span>Fare</span>

          <button
            role="button"
            tabindex="0"
            class="tips"
            aria-label="View fare breakdown details"
            data-tippy-table="<%= fareTableJSON %>">
            <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
          </button>
        </div>
        <div class="detail-value" aria-label="Fare CNY 900">CNY 900</div>
      </div>

      <div class="detail-divider" role="separator" aria-hidden="true"></div>

      <div class="detail-features" role="group" aria-label="Flight features">
        <div class="feature-item">
          <div class="feature-icon cabin-icon" role="img" aria-label="Cabin class icon"></div>
          <div class="feature-name">Cabin Class</div>
          <button
            class="feature-value feature-value-hover"
            aria-label="Cabin class U+A, click for details"
            data-tippy-table="<%= UATableJSON %>">
            U+A
          </button>
        </div>

        <div class="feature-item">
          <div class="feature-icon baggage-icon" role="img" aria-label="Baggage icon"></div>
          <div class="feature-name">
            Baggage
            <button
              role="button"
              tabindex="0"
              class="tips"
              aria-label="View fare breakdown details"
              data-tippy-content="tips tips tips tips tips">
              <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </button>
          </div>
          <a href="#" class="feature-value feature-value-link" aria-label="Baggage allowance 1 piece">1 Pieces</a>
        </div>

        <div class="feature-item">
          <div class="feature-icon refund-icon" role="img" aria-label="Refund icon"></div>
          <div class="feature-name">Refund</div>
          <button
            class="feature-value feature-value-hover"
            role="button"
            tabindex="0"
            aria-label="Refund fee 300 plus, click for details"
            data-tippy-table="<%= refundTableJSON %>">
            300+
          </button>
        </div>

        <div class="feature-item">
          <div class="feature-icon change-icon" role="img" aria-label="Change icon"></div>
          <div class="feature-name">Change</div>
          <button
            class="feature-value feature-value-hover"
            role="button"
            tabindex="0"
            aria-label="Change fee 300 plus, click for details"
            data-tippy-table="<%= changeTableJSON %>">
            300+
          </button>
        </div>

        <div class="feature-item">
          <div class="feature-icon mileage-icon" role="img" aria-label="Mileage icon"></div>
          <div class="feature-name">Base Mileage</div>
          <div class="feature-value" aria-label="Base mileage plus 1142 points">+1142</div>
        </div>

        <div class="feature-item">
          <div class="feature-icon bonus-icon" role="img" aria-label="Bonus icon"></div>
          <div class="feature-name">
            Bonus Mileage
            <button
              role="button"
              tabindex="0"
              class="tips"
              aria-label="View fare breakdown details"
              data-tippy-content="tips tips tips tips tips">
              <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
            </button>
          </div>
          <div class="feature-value" aria-label="Bonus mileage plus 1142 points">+1142</div>
        </div>
      </div>
    </div>
  </div>
  <button class="price-footer" tabindex="0" aria-label="Book this flight for CNY 7,999">
    <div class="book-button">
      <div class="book-icon" role="img" aria-label="Book icon" aria-hidden="true"></div>
      <span>BOOK</span>
    </div>
  </button>
</div>
