<%
  const _isTransfer = typeof isTransfer !== 'undefined' && isTransfer;
  const _isStop = typeof isStop !== 'undefined' && isStop;
  const _className = typeof className !== 'undefined' ? className : '';
  const _routeType = typeof routeType !== 'undefined' ? routeType: undefined; // 1:单程 2:往返 3:多段

  const mealTableJSON = JSON.stringify({
    notesTitle:'Tips:',
    notes: [
      '1. The specific types of meals are subject to the actual equipment of the flight.',
      '2. Meals for codeshare flights shall be based on the actual operating flight;',
      '3. In case of special circumstances such as flight delays, changes in aircraft types, or sudden public health emergencies that may result in adjustments to the meal plan, please refer to the actual configuration of the flight;'
    ],
  });
%>

<div class="flight-info <%= _className %>" role="region" aria-label="Flight information">
  <div class="flight-info-header">
    <div class="flight-time-container">
      <div class="flight-time-wrapper">
        <div class="flight-duration">
          <div class="duration-tag" aria-label="Flight duration 2 hours 5 minutes">2h 5min</div>
        </div>

        <div class="flight-route">
          <div class="departure-time" aria-label="Departure time 12:30">12:30</div>
          <div class="flight-route-line" role="img" aria-label="Flight route">
            <div class="line-left"></div>
            <!-- 经停 -->
            <% if (_isTransfer) { %>
            <div class="transfer-card">
              <div class="transfer-city" aria-label="transfer at Wuxi">Transfer: Wuxi</div>
              <div class="transfer-city" aria-label="transfer at Wuxi">Transfer: Wuxi</div>
            </div>
            <% } else if(_isStop) { %>
            <div class="stop-card">
              <div class="stop-city" aria-label="stop at Wuxi">Stopping in Wuxi</div>
              <div class="stop-city" aria-label="stop at Wuxi">Stopping in Wuxi</div>
            </div>
            <% } else { %>
            <div class="transfer-city">
              <img src="../../images/flightOptions/flight.svg" alt="Direct flight" />
            </div>
            <% } %>
            <div class="line-right"></div>
          </div>
          <div class="arrival-time" aria-label="Arrival time 14:35">
            14:35
            <span class="add1">+1</span>
          </div>
        </div>

        <div class="flight-place">
          <div class="departure-airport" aria-label="Departure airport">Shenzhen Bao'an International Airport</div>
          <div class="arrival-airport" aria-label="Arrival airport">Beijing Capital International Airport</div>
        </div>
      </div>
    </div>
    <div class="flight-details">
      <div class="flight-details-left">
        <div class="flight-details-left-content">
          <div class="flight-number">
            <img class="airline-logo" src="../../images/flightOptions/airline-logo.svg" alt="Airline logo" />
            <div class="flight-code" aria-label="Flight number ZH8034">ZH8034</div>
          </div>
          <div class="aircraft-type" aria-label="Aircraft type Airbus A320">Airbus A320</div>
          <div class="flight-tags">
            <span class="tag-shared" aria-label="Shared flight">共享</span>
          </div>
        </div>
        <div class="flight-details-left-content">
          <div class="flight-number">
            <img class="airline-logo" src="../../images/flightOptions/airline-logo.svg" alt="Airline logo" />
            <div class="flight-code" aria-label="Flight number ZH8034">ZH8034</div>
          </div>
          <div class="aircraft-type" aria-label="Aircraft type Airbus A320">Airbus A320</div>
          <div class="flight-tags">
            <span class="tag-shared" aria-label="Shared flight">共享</span>
          </div>
        </div>
      </div>

      <% if (_isStop) { %>
      <button class="flight-details-link" onclick="modalFlightDetails.open()" aria-label="View flight details">
        <span>Flight details</span>
        <i class="icon arrow-icon" aria-hidden="true"></i>
      </button>
      <% } else { %>
      <div
        class="flight-details-link"
        role="button"
        tabindex="0"
        aria-label="Meal information"
        data-tippy-table="<%= mealTableJSON %>">
        <img class="icon" src="../../images/flightOptions/meals.svg" alt="Meal icon" />
        <span>Meal</span>
      </div>
      <% } %>
    </div>
  </div>
</div>
