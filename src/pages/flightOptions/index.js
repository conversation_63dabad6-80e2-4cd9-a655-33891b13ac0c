tippy('[data-tippy-content]', {
  appendTo: () => document.body,
  allowHTML: true,
  interactive: true,
});

const modal = new SZModal({
  // title: 'Re-query Flight',
  // okText: 'Search',
  contentDom: $('#modal-content-flight'),
  onOk: () => {
    console.log('ok');
  },
});
// modal.open()

const modalFlightDetails = new SZModal({
  modalDom: $('#flight-details'),
  // title: 'Transfer for 1 times',
  // okText: 'Back',
  contentDom: $('#flight-details-content'),
  cancelText: false,
  // onOk: () => {
  //   console.log('ok')
  // },
});
// modalFlightDetails.open()
