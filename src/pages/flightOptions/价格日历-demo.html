<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>价格日历 - 日期范围功能演示</title>
    <link rel="stylesheet" href="../../css/common.css" />

    <style>
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 20px;
        min-height: 100vh;
      }
      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }
      .demo-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }
      .demo-header h1 {
        margin: 0 0 10px 0;
        font-size: 28px;
        font-weight: 600;
      }
      .demo-header p {
        margin: 0;
        opacity: 0.9;
        font-size: 16px;
      }
      .demo-content {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 0;
      }
      .calendar-panel {
        padding: 30px;
        background: #f8f9fa;
      }
      .control-panel {
        background: white;
        padding: 30px;
        border-left: 1px solid #e9ecef;
      }
      .control-section {
        margin-bottom: 30px;
      }
      .control-section h3 {
        margin: 0 0 15px 0;
        font-size: 16px;
        color: #495057;
        font-weight: 600;
        padding-bottom: 8px;
        border-bottom: 2px solid #e9ecef;
      }
      .form-group {
        margin-bottom: 15px;
      }
      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-size: 14px;
        font-weight: 500;
        color: #495057;
      }
      .form-group input {
        width: 100%;
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s;
        box-sizing: border-box;
      }
      .form-group input:focus {
        outline: none;
        border-color: #667eea;
      }
      .btn {
        width: 100%;
        padding: 10px 15px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        margin-bottom: 8px;
      }
      .btn-primary {
        background: #667eea;
        color: white;
      }
      .btn-primary:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
      }
      .btn-secondary {
        background: #6c757d;
        color: white;
      }
      .btn-secondary:hover {
        background: #5a6268;
      }
      .btn-danger {
        background: #dc3545;
        color: white;
      }
      .btn-danger:hover {
        background: #c82333;
      }
      .info-panel {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        font-size: 14px;
        line-height: 1.5;
      }
      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      .info-item:last-child {
        margin-bottom: 0;
      }
      .info-label {
        color: #6c757d;
        font-weight: 500;
      }
      .info-value {
        color: #495057;
        font-weight: 600;
      }
      .preset-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
      }
      .preset-buttons .btn {
        margin-bottom: 0;
        font-size: 12px;
        padding: 8px 10px;
      }
      @media (max-width: 768px) {
        .demo-content {
          grid-template-columns: 1fr;
        }
        .control-panel {
          border-left: none;
          border-top: 1px solid #e9ecef;
        }
      }
    </style>
  </head>
  <body>
    <!-- <div id="price-calendar"></div> -->

    <div class="demo-container">
      <div class="demo-header">
        <h1>🗓️ 价格日历 - 日期范围功能</h1>
        <p>演示最小日期、最大日期限制和相关交互行为</p>
      </div>

      <div class="demo-content">
        <!-- 日历面板 -->
        <div class="calendar-panel">
          <div id="price-calendar"></div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
          <!-- 当前状态 -->
          <div class="control-section">
            <h3>📊 当前状态</h3>
            <div class="info-panel">
              <div class="info-item">
                <span class="info-label">选中日期:</span>
                <span class="info-value" id="selectedDate">无</span>
              </div>
              <div class="info-item">
                <span class="info-label">当前月份:</span>
                <span class="info-value" id="currentMonth">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">最小日期:</span>
                <span class="info-value" id="minDateDisplay">无限制</span>
              </div>
              <div class="info-item">
                <span class="info-label">最大日期:</span>
                <span class="info-value" id="maxDateDisplay">无限制</span>
              </div>
            </div>
          </div>

          <!-- 日期范围设置 -->
          <div class="control-section">
            <h3>⚙️ 日期范围设置</h3>
            <div class="form-group">
              <label>最小日期:</label>
              <input type="date" id="minDate" />
            </div>
            <div class="form-group">
              <label>最大日期:</label>
              <input type="date" id="maxDate" />
            </div>
            <button class="btn btn-primary" onclick="setDateRange()">应用日期范围</button>
            <button class="btn btn-secondary" onclick="clearDateRange()">清除限制</button>
          </div>

          <!-- 快速设置 -->
          <div class="control-section">
            <h3>🚀 快速设置</h3>
            <div class="preset-buttons">
              <button class="btn btn-secondary" onclick="setThisMonth()">本月</button>
              <button class="btn btn-secondary" onclick="setNextMonth()">下月</button>
              <button class="btn btn-secondary" onclick="setNext3Months()">未来3月</button>
              <button class="btn btn-secondary" onclick="setNext6Months()">未来6月</button>
            </div>
          </div>

          <!-- 测试功能 -->
          <div class="control-section">
            <h3>🧪 测试功能</h3>
            <button class="btn btn-primary" onclick="generateNewData()">生成新数据</button>
            <button class="btn btn-secondary" onclick="goToToday()">回到今天</button>
            <button class="btn btn-secondary" onclick="testDateRange()">测试日期范围</button>
          </div>
        </div>
      </div>
    </div>

    <script>
      window.jQuery || document.write('<script src="../../plugins/jquery-3.7.1.min.js"><\/script>');
    </script>
    <script src="../../js/components-sz-price-calendar.js"></script>

    <script>
      // 生成测试数据
      function generateTestData() {
        const data = [];
        const today = new Date();

        // 生成未来3个月的数据
        for (let i = 0; i < 90; i++) {
          const date = new Date(today);
          date.setDate(today.getDate() + i);

          // 随机生成价格，周末价格稍高
          const isWeekend = date.getDay() === 0 || date.getDay() === 6;
          const basePrice = isWeekend ? 2200 : 1800;
          const price = basePrice + Math.floor(Math.random() * 800);

          // 随机设置一些日期不可用
          const available = Math.random() > 0.1;

          data.push({
            date: date.toISOString().split('T')[0],
            price: price,
            available: available,
          });
        }

        return data;
      }

      // 初始化日历
      const testData = generateTestData();

      // 更新状态显示
      function updateStatus() {
        // const selectedDate = calendar.getSelectedDate();
        // const currentMonth = calendar.getCurrentMonth();
        // const dateRange = calendar.getDateRange();
        // document.getElementById('selectedDate').textContent = selectedDate || '无';
        // document.getElementById('currentMonth').textContent =
        //   currentMonth.getFullYear() + '年' + (currentMonth.getMonth() + 1) + '月';
        // document.getElementById('minDateDisplay').textContent = dateRange.minDate || '无限制';
        // document.getElementById('maxDateDisplay').textContent = dateRange.maxDate || '无限制';
      }

      // 设置日期范围
      function setDateRange() {
        const minDate = document.getElementById('minDate').value;
        const maxDate = document.getElementById('maxDate').value;

        console.log('设置日期范围:', { minDate, maxDate });

        calendar.setDateRange(minDate || null, maxDate || null);
        updateStatus();

        const range = calendar.getDateRange();
        console.log('设置后的范围:', range);

        // 测试最小日期是否可选
        if (minDate) {
          const testDate = new Date(minDate);
          const isOutOfRange = calendar.isDateOutOfRange(testDate);
          console.log(`最小日期 ${minDate} 是否超出范围:`, isOutOfRange);
        }

        alert(`日期范围已设置:\\n最小日期: ${range.minDate || '无'}\\n最大日期: ${range.maxDate || '无'}`);
      }

      // 清除日期范围
      function clearDateRange() {
        calendar.setDateRange(null, null);
        document.getElementById('minDate').value = '';
        document.getElementById('maxDate').value = '';
        updateStatus();
        alert('日期范围已清除');
      }

      // 快速设置 - 本月
      function setThisMonth() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        calendar.setDateRange(firstDay, lastDay);
        document.getElementById('minDate').value = firstDay.toISOString().split('T')[0];
        document.getElementById('maxDate').value = lastDay.toISOString().split('T')[0];
        updateStatus();
      }

      // 快速设置 - 下月
      function setNextMonth() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 2, 0);

        calendar.setDateRange(firstDay, lastDay);
        document.getElementById('minDate').value = firstDay.toISOString().split('T')[0];
        document.getElementById('maxDate').value = lastDay.toISOString().split('T')[0];
        updateStatus();
      }

      // 快速设置 - 未来3个月
      function setNext3Months() {
        const today = new Date();
        const endDate = new Date(today);
        endDate.setMonth(today.getMonth() + 3);

        calendar.setDateRange(today, endDate);
        document.getElementById('minDate').value = today.toISOString().split('T')[0];
        document.getElementById('maxDate').value = endDate.toISOString().split('T')[0];
        updateStatus();
      }

      // 快速设置 - 未来6个月
      function setNext6Months() {
        const today = new Date();
        const endDate = new Date(today);
        endDate.setMonth(today.getMonth() + 6);

        calendar.setDateRange(today, endDate);
        document.getElementById('minDate').value = today.toISOString().split('T')[0];
        document.getElementById('maxDate').value = endDate.toISOString().split('T')[0];
        updateStatus();
      }

      // 生成新数据
      function generateNewData() {
        const newData = generateTestData();
        calendar.updateData(newData);
        alert('数据已更新！');
      }

      // 回到今天
      function goToToday() {
        const today = new Date();
        calendar.setCurrentMonth(today);
        updateStatus();
      }

      // 测试日期范围逻辑
      function testDateRange() {
        const minDate = document.getElementById('minDate').value;
        const maxDate = document.getElementById('maxDate').value;

        if (!minDate && !maxDate) {
          alert('请先设置日期范围');
          return;
        }

        console.log('=== 日期范围测试 ===');
        console.log('设置的范围:', { minDate, maxDate });

        // 测试边界日期
        const testDates = [];

        if (minDate) {
          const min = new Date(minDate);
          testDates.push({
            label: '最小日期前一天',
            date: new Date(min.getTime() - 24 * 60 * 60 * 1000),
          });
          testDates.push({
            label: '最小日期当天',
            date: min,
          });
          testDates.push({
            label: '最小日期后一天',
            date: new Date(min.getTime() + 24 * 60 * 60 * 1000),
          });
        }

        if (maxDate) {
          const max = new Date(maxDate);
          testDates.push({
            label: '最大日期前一天',
            date: new Date(max.getTime() - 24 * 60 * 60 * 1000),
          });
          testDates.push({
            label: '最大日期当天',
            date: max,
          });
          testDates.push({
            label: '最大日期后一天',
            date: new Date(max.getTime() + 24 * 60 * 60 * 1000),
          });
        }

        testDates.forEach(({ label, date }) => {
          const isOutOfRange = calendar.isDateOutOfRange(date);
          const dateStr = calendar.formatDate(date);
          console.log(`${label} (${dateStr}): ${isOutOfRange ? '超出范围' : '在范围内'}`);
        });

        alert('测试完成，请查看控制台输出');
      }

      // 初始化状态显示
      updateStatus();

      // 设置默认的日期范围示例
      const today = new Date();
      const nextMonth = new Date(today);
      nextMonth.setMonth(today.getMonth() + 2);

      document.getElementById('minDate').value = today.toISOString().split('T')[0];
      document.getElementById('maxDate').value = nextMonth.toISOString().split('T')[0];

      console.log('价格日历组件已初始化');
      console.log('测试数据:', testData);
      console.log('支持的API方法:', {
        setDateRange: 'calendar.setDateRange(minDate, maxDate)',
        getDateRange: 'calendar.getDateRange()',
        setMinDate: 'calendar.setMinDate(date)',
        setMaxDate: 'calendar.setMaxDate(date)',
      });
    </script>

    <script>
      const calendar = new PriceCalendar({
        container: '#price-calendar',
        // data: testData,
        data: [
          {
            date: '2025-07-16',
            available: true,
          },
          {
            date: '2025-07-17',
            available: true,
          },
          {
            date: '2025-07-18',
            available: true,
          },
          {
            date: '2025-07-19',
            available: true,
          },
          {
            date: '2025-07-20',
            available: true,
          },
          {
            date: '2025-07-21',
            available: true,
          },
          {
            date: '2025-07-22',
            available: true,
          },
          {
            date: '2025-07-23',
            available: true,
          },
          {
            date: '2025-07-24',
            available: true,
          },
          {
            date: '2025-07-25',
            available: true,
          },
          {
            date: '2025-07-26',
            available: true,
          },
          {
            date: '2025-07-27',
            available: true,
          },
          {
            date: '2025-07-28',
            available: true,
          },
          {
            date: '2025-07-29',
            available: true,
          },
          {
            date: '2025-07-30',
            available: true,
          },
          {
            date: '2025-07-31',
            available: true,
          },
          {
            date: '2025-08-01',
            available: true,
          },
          {
            date: '2025-08-02',
            available: true,
          },
          {
            date: '2025-08-03',
            available: true,
          },
          {
            date: '2025-08-04',
            available: true,
          },
          {
            date: '2025-08-05',
            available: true,
          },
          {
            date: '2025-08-06',
            available: true,
          },
          {
            date: '2025-08-07',
            available: true,
          },
          {
            date: '2025-08-08',
            available: true,
          },
          {
            date: '2025-08-09',
            available: true,
          },
          {
            date: '2025-08-10',
            available: true,
          },
          {
            date: '2025-08-11',
            available: true,
          },
          {
            date: '2025-08-12',
            available: true,
          },
          {
            date: '2025-08-13',
            available: true,
          },
          {
            date: '2025-08-14',
            available: true,
          },
          {
            date: '2025-08-15',
            available: true,
            price: 2320,
          },
          {
            date: '2025-08-16',
            available: true,
            price: 2320,
          },
          {
            date: '2025-08-17',
            available: true,
            price: 1930,
          },
          {
            date: '2025-08-18',
            available: true,
          },
          {
            date: '2025-08-19',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-20',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-21',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-22',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-23',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-24',
            available: true,
          },
          {
            date: '2025-08-25',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-26',
            available: true,
          },
          {
            date: '2025-08-27',
            available: true,
          },
          {
            date: '2025-08-28',
            available: true,
          },
          {
            date: '2025-08-29',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-30',
            available: true,
            price: 1870,
          },
          {
            date: '2025-08-31',
            available: true,
          },
          {
            date: '2025-09-01',
            available: true,
            price: 16300,
          },
          {
            date: '2025-09-02',
            available: true,
          },
          {
            date: '2025-09-03',
            available: true,
          },
          {
            date: '2025-09-04',
            available: true,
          },
          {
            date: '2025-09-05',
            available: true,
            price: 16300,
          },
          {
            date: '2025-09-06',
            available: true,
          },
          {
            date: '2025-09-07',
            available: true,
          },
          {
            date: '2025-09-08',
            available: true,
          },
          {
            date: '2025-09-09',
            available: true,
          },
          {
            date: '2025-09-10',
            available: true,
          },
          {
            date: '2025-09-11',
            available: true,
            price: 1870,
          },
          {
            date: '2025-09-12',
            available: true,
            price: 1870,
          },
          {
            date: '2025-09-13',
            available: true,
          },
          {
            date: '2025-09-14',
            available: true,
          },
        ],
        currency: 'CNY',
        onDateSelect: function (dateStr, dayData) {
          console.log('=== 日期选择调试信息 ===');
          console.log('原始日期字符串:', dateStr);
          console.log('日期数据:', dayData);

          // 使用this引用当前日历实例
          const parsedDate = this._parseDate(dateStr);
          console.log('解析后的日期对象:', parsedDate);
          console.log('日期对象详情:', {
            year: parsedDate.getFullYear(),
            month: parsedDate.getMonth() + 1,
            date: parsedDate.getDate(),
          });
          console.log('重新格式化的日期:', this.formatDate(parsedDate));
          console.log('========================');

          updateStatus();
        },
        onMonthChange: function (newMonth) {
          console.log('月份变化:', newMonth);
          updateStatus();
        },
      });
    </script>
  </body>
</html>
