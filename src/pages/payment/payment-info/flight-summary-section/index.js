$(document).ready(function () {
  // 退改签条目点击切换逻辑
  $('.refund-change-item').on('click', function () {
    // 获取目标区域的类名
    const targetSection = $(this).attr('data-target');
    const $targetElement = $(this).closest('.flight-summary-section').find(`.${targetSection}`);
    const $arrowIcon = $(this).find('.arrow-icon');

    // 如果目标元素不存在则返回
    if ($targetElement.length === 0) return;

    // 检查当前区域是否可见
    const isVisible = $targetElement.is(':visible');
    if (isVisible) {
      // 隐藏当前区域
      $targetElement.hide();
      $arrowIcon.removeClass('rotated');
    } else {
      // 先隐藏所有其他区域
      $('.section-content').hide();

      // 重置所有箭头图标
      $('.refund-change-item .arrow-icon').removeClass('rotated');

      // 显示目标区域并旋转对应箭头
      $targetElement.show();
      $arrowIcon.addClass('rotated');
    }
  });

  new SZTable({
    container: '#table-payment-info-refund-1',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        colSpan: 2,
        onCell: (_, index) => {
          if (index === 0) {
            return { rowSpan: 2 };
          }
          if (index === 1) {
            return { rowSpan: 0 };
          }
          if (index === 2) {
            return { rowSpan: 2 };
          }
          if (index === 3) {
            return { rowSpan: 0 };
          }
          return {};
        },
      },
      {
        title: '',
        dataIndex: 'isUse',
        colSpan: 0,
      },
      {
        title: 'Refund',
        dataIndex: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '2',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
      {
        key: '3',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '4',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
    ],
    notes: [
      'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  });

  new SZTable({
    container: '#table-payment-info-refund-2',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        colSpan: 2,
        onCell: (_, index) => {
          if (index === 0) {
            return { rowSpan: 2 };
          }
          if (index === 1) {
            return { rowSpan: 0 };
          }
          if (index === 2) {
            return { rowSpan: 2 };
          }
          if (index === 3) {
            return { rowSpan: 0 };
          }
          return {};
        },
      },
      {
        title: '',
        dataIndex: 'isUse',
        colSpan: 0,
      },
      {
        title: 'Refund',
        dataIndex: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '2',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
      {
        key: '3',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '4',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
    ],
    notes: [
      'When the refund, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No refund will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  });

  new SZTable({
    container: '#table-payment-info-change-1',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        colSpan: 2,
        onCell: (_, index) => {
          if (index === 0) {
            return { rowSpan: 2 };
          }
          if (index === 1) {
            return { rowSpan: 0 };
          }
          if (index === 2) {
            return { rowSpan: 2 };
          }
          if (index === 3) {
            return { rowSpan: 0 };
          }
          return {};
        },
      },
      {
        title: '',
        dataIndex: 'isUse',
        colSpan: 0,
      },
      {
        title: 'Change',
        dataIndex: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '2',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
      {
        key: '3',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY660.0',
      },
      {
        key: '4',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY660.0',
      },
    ],
    notes: [
      'When the change, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No change will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  });

  new SZTable({
    container: '#table-payment-info-change-2',
    columns: [
      {
        title: 'Change of Flight/Cabin Class',
        dataIndex: 'time',
        colSpan: 2,
        onCell: (_, index) => {
          if (index === 0) {
            return { rowSpan: 2 };
          }
          if (index === 1) {
            return { rowSpan: 0 };
          }
          if (index === 2) {
            return { rowSpan: 2 };
          }
          if (index === 3) {
            return { rowSpan: 0 };
          }
          return {};
        },
      },
      {
        title: '',
        dataIndex: 'isUse',
        colSpan: 0,
      },
      {
        title: 'Change',
        dataIndex: 'amount',
      },
    ],
    dataSource: [
      {
        key: '1',
        time: 'After Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '2',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
      {
        key: '3',
        time: 'Before Takeoff',
        isUse: 'All Unused',
        amount: 'CNY661.0',
      },
      {
        key: '4',
        time: '',
        isUse: 'Partially Used',
        amount: 'CNY661.0',
      },
    ],
    notes: [
      'When the change, change and rescheduling rules for multiple flight segments differ, the strictest standard shall be applied.',
      'Refunds must be requested within one year from the start date of the first - leg journey (if the first flight segment of the ticket remains unused, it is calculated from the date of ticket issuance). No change will be processed after the expiration of this period. For tickets that do not allow refunds, the fuel surcharge and other related fees will not be refunded.',
    ],
  });
});
