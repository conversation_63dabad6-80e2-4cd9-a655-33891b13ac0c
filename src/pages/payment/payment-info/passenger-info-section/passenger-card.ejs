<%

  const psgTableJSON = JSON.stringify({
  showHeader: false,
  bordered: false,
  columns: [
    {
      title: '',
      dataIndex: 'name',
      align: 'left',
    },
    {
      title: '',
      dataIndex: 'amount',
      align: 'right',
    },
  ],
  dataSource: [
    {
      name: 'Ticket Face Value',
      amount: 'CNY 4,600',
    },
    {
      name: 'CN Airport Tax',
      amount: 'CNY 18',
    },
    {
      name: 'G3 Airport Construction Fee',
      amount: 'CNY 34',
    },
    {
      name: 'HK Airport Passenger Departure Tax',
      amount: 'CNY 53',
    },
    {
      name: '15 Airport Passenger Security Fee',
      amount: 'CNY 33',
    },
    {
      name: 'YQ Carrier Fuel Surcharge',
      amount: 'CNY 270',
    },
  ],
});

%>

<div class="passenger-card">
  <div class="passenger-header">
    <span>Passenger 1</span>
  </div>
  <div class="passenger-info">
    <div class="info-item">
      <div class="info-label">Name</div>
      <div class="info-value"><PERSON> San</div>
    </div>
    <div class="info-item">
      <div class="info-label">Passenger Type</div>
      <div class="info-value">Adult</div>
    </div>
    <div class="info-item">
      <div class="info-label">Voucher Type</div>
      <div class="info-value">ID Card</div>
    </div>
    <div class="info-item">
      <div class="info-label">Voucher Number</div>
      <div class="info-value">15911111111</div>
    </div>
    <div class="info-item">
      <div class="info-label">Frequent Flyer Number</div>
      <div class="info-value">1234567890</div>
    </div>
    <div class="info-item">
      <div class="info-label">Date of Birth</div>
      <div class="info-value">1997.05.04</div>
    </div>
    <div class="info-item">
      <div class="info-label">Contact Information</div>
      <div class="info-value">15112345678</div>
    </div>
    <div class="info-item">
      <div class="info-label">Ticket Number</div>
      <div class="info-value">5103151981222/23/24/25</div>
    </div>
    <div class="info-item">
      <div class="info-label">Subtotal</div>
      <div class="info-value highlight">
        Miles500+CNY 5,008
        <div class="__text-button" role="button" tabindex="0" data-tippy-table="<%= psgTableJSON %>">
          <span class="icon-zh icon-zh-ask" aria-hidden="true"></span>
        </div>
      </div>
    </div>
  </div>
</div>
