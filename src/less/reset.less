@import './variables';

/* reset.css样式重置文件 */
/* margin/padding重置 */
body,
h1,
h2,
h3,
ul,
ol,
li,
p,
dl,
dt,
dd {
  padding: 0;
  margin: 0;
}

body {
  overflow-x: hidden;
}

*,
*::before,
*::after {
  box-sizing: border-box; // 1
}

/* a元素重置 */
a {
  text-decoration: none;
  color: #333;
}

/* img的vertical-align重置 */
img {
  vertical-align: top;
}

/* ul, ol, li重置 */
ul,
ol,
li {
  list-style: none;
}

/* 对斜体元素重置 */
i,
em {
  font-style: normal;
}

img {
  vertical-align: middle;
  border-style: none; // Remove the border on images inside links in IE 10-.
}

html {
  font-family: @font-family-sans-serif; // 2
  line-height: 1.15; // 3
  font-size: 20px;
  -webkit-text-size-adjust: 100%; // 4
  -ms-text-size-adjust: 100%; // 4
  -ms-overflow-style: scrollbar; // 5
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); // 6
}

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px #ffffff inset !important;
}

button {
  cursor: pointer;
  border-radius: 0;
  background: none;
  border: none;
  padding: 0;
}
